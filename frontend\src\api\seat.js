import http from "./http";

export default {
  // 获取自习室列表
  getRooms() {
    return http.get("/seat/rooms");
  },

  // 获取自习室详情
  getRoomById(roomId) {
    return http.get(`/seat/rooms/${roomId}`);
  },

  // 获取自习室座位列表
  getSeatsByRoom(roomId, date) {
    return http.get(`/seat/rooms/${roomId}/seats`, { params: { date } });
  },

  // 获取座位详情
  getSeatById(seatId) {
    return http.get(`/seat/seats/${seatId}`);
  },

  // 获取座位可用时间段
  getSeatTimeSlots(seatId, date) {
    return http.get(`/seat/seats/${seatId}/time-slots`, { params: { date } });
  },

  // 创建座位预约
  createReservation(seatId, startTime, endTime) {
    return http.post("/seat/reservations", { seatId, startTime, endTime });
  },

  // 获取我的预约列表
  getMyReservations() {
    return http.get("/seat/reservations/my");
  },

  // 获取预约详情
  getReservationById(reservationId) {
    return http.get(`/seat/reservations/${reservationId}`);
  },

  // 获取活跃预约
  getActiveReservation() {
    return http.get("/seat/reservations/active");
  },

  // 签到
  checkIn(requestData) {
    return http.post("/seat/reservations/check_in", requestData);
  },

  // 签退
  checkOut(requestData) {
    return http.post("/seat/reservations/check_out", requestData);
  },

  // 取消预约
  cancelReservation(reservationId) {
    return http.post(`/seat/reservations/${reservationId}/cancel`);
  },

  // 获取操作记录
  getOperationRecords(params) {
    return http.get("/seat/operations", { params });
  },

  // 获取预约历史记录
  getReservationHistory(params) {
    return http.get("/seat/reservations/history", { params });
  },
};
