<template>
  <div class="checkin-checkout-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>签到签退</h2>
        </div>
      </template>

      <!-- 当前活跃预约 -->
      <div v-if="activeReservation" class="active-reservation">
        <el-alert
          :title="`当前预约: ${activeReservation.seat.room.name} - ${activeReservation.seat.seat_number}`"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="reservation-info">
              <p><strong>预约时间:</strong> {{ formatDateTime(activeReservation.start_time) }} - {{ formatDateTime(activeReservation.end_time) }}</p>
              <p><strong>状态:</strong> 
                <el-tag :type="getStatusType(activeReservation.status)">
                  {{ getStatusText(activeReservation.status) }}
                </el-tag>
              </p>
              <p v-if="activeReservation.check_in_time">
                <strong>签到时间:</strong> {{ formatDateTime(activeReservation.check_in_time) }}
              </p>
            </div>
          </template>
        </el-alert>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            v-if="activeReservation.status === 'pending'"
            type="primary"
            size="large"
            :loading="checkingIn"
            @click="showCheckInDialog"
          >
            <el-icon><Location /></el-icon>
            签到
          </el-button>

          <el-button
            v-if="activeReservation.status === 'checked_in'"
            type="success"
            size="large"
            :loading="checkingOut"
            @click="showCheckOutDialog"
          >
            <el-icon><CircleCheck /></el-icon>
            签退
          </el-button>

          <el-button
            v-if="activeReservation.status === 'pending'"
            type="danger"
            size="large"
            :loading="cancelling"
            @click="showCancelDialog"
          >
            <el-icon><Close /></el-icon>
            取消预约
          </el-button>
        </div>
      </div>

      <!-- 无活跃预约 -->
      <div v-else class="no-reservation">
        <el-empty description="当前没有活跃的预约">
          <el-button type="primary" @click="$router.push('/seat/reservation')">
            去预约座位
          </el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 签到对话框 -->
    <el-dialog
      v-model="checkInDialogVisible"
      title="确认签到"
      width="400px"
      :before-close="handleDialogClose"
    >
      <div class="dialog-content">
        <p>确认签到到以下座位？</p>
        <div class="seat-info">
          <p><strong>自习室:</strong> {{ activeReservation?.seat.room.name }}</p>
          <p><strong>座位:</strong> {{ activeReservation?.seat.seat_number }}</p>
          <p><strong>预约时间:</strong> {{ formatDateTime(activeReservation?.start_time) }} - {{ formatDateTime(activeReservation?.end_time) }}</p>
        </div>

        <!-- 签名选项 -->
        <el-form v-if="userHasPublicKey" :model="checkInForm" label-position="top">
          <el-form-item label="使用数字签名验证身份">
            <el-switch
              v-model="checkInForm.useSignature"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="checkInDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="checkingIn" @click="handleCheckIn">
            确认签到
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 签退对话框 -->
    <el-dialog
      v-model="checkOutDialogVisible"
      title="确认签退"
      width="400px"
      :before-close="handleDialogClose"
    >
      <div class="dialog-content">
        <p>确认签退以下座位？</p>
        <div class="seat-info">
          <p><strong>自习室:</strong> {{ activeReservation?.seat.room.name }}</p>
          <p><strong>座位:</strong> {{ activeReservation?.seat.seat_number }}</p>
          <p><strong>签到时间:</strong> {{ formatDateTime(activeReservation?.check_in_time) }}</p>
        </div>

        <!-- 签名选项 -->
        <el-form v-if="userHasPublicKey" :model="checkOutForm" label-position="top">
          <el-form-item label="使用数字签名验证身份">
            <el-switch
              v-model="checkOutForm.useSignature"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="checkOutDialogVisible = false">取消</el-button>
          <el-button type="success" :loading="checkingOut" @click="handleCheckOut">
            确认签退
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog
      v-model="cancelDialogVisible"
      title="取消预约"
      width="400px"
      :before-close="handleDialogClose"
    >
      <div class="dialog-content">
        <p>确认取消以下预约？</p>
        <div class="seat-info">
          <p><strong>自习室:</strong> {{ activeReservation?.seat.room.name }}</p>
          <p><strong>座位:</strong> {{ activeReservation?.seat.seat_number }}</p>
          <p><strong>预约时间:</strong> {{ formatDateTime(activeReservation?.start_time) }} - {{ formatDateTime(activeReservation?.end_time) }}</p>
        </div>
        <el-alert
          title="注意：取消预约可能会影响您的信誉分"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">不取消</el-button>
          <el-button type="danger" :loading="cancelling" @click="handleCancel">
            确认取消
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from "vue";
import { useStore } from "vuex";
import { ElMessage, ElMessageBox } from "element-plus";
import { Location, CircleCheck, Close } from "@element-plus/icons-vue";
import { SM2Crypto } from "@/utils/crypto";

export default {
  name: "CheckInOut",
  components: {
    Location,
    CircleCheck,
    Close,
  },
  setup() {
    const store = useStore();

    // 响应式数据
    const activeReservation = ref(null);
    const checkingIn = ref(false);
    const checkingOut = ref(false);
    const cancelling = ref(false);

    // 对话框状态
    const checkInDialogVisible = ref(false);
    const checkOutDialogVisible = ref(false);
    const cancelDialogVisible = ref(false);

    // 表单数据
    const checkInForm = reactive({
      useSignature: false,
    });

    const checkOutForm = reactive({
      useSignature: false,
    });

    // 计算属性
    const userHasPublicKey = computed(() => {
      return store.getters["user/userInfo"]?.public_key;
    });

    // 方法
    const loadActiveReservation = async () => {
      try {
        const response = await store.dispatch("seat/getActiveReservation");
        activeReservation.value = response;
      } catch (error) {
        if (error.response?.status !== 404) {
          ElMessage.error("获取活跃预约失败");
        }
      }
    };

    const showCheckInDialog = () => {
      checkInDialogVisible.value = true;
    };

    const showCheckOutDialog = () => {
      checkOutDialogVisible.value = true;
    };

    const showCancelDialog = () => {
      cancelDialogVisible.value = true;
    };

    const handleCheckIn = async () => {
      try {
        checkingIn.value = true;

        const requestData = {
          reservation_code: activeReservation.value.reservation_code,
        };

        // 如果启用签名
        if (checkInForm.useSignature && userHasPublicKey.value) {
          const privateKey = localStorage.getItem("sm2_private_key");
          if (privateKey) {
            const signature = SM2Crypto.sign(
              privateKey,
              activeReservation.value.reservation_code
            );
            requestData.signature = signature;
          }
        }

        await store.dispatch("seat/checkIn", requestData);
        
        ElMessage.success("签到成功");
        checkInDialogVisible.value = false;
        await loadActiveReservation();
      } catch (error) {
        ElMessage.error(error.message || "签到失败");
      } finally {
        checkingIn.value = false;
      }
    };

    const handleCheckOut = async () => {
      try {
        checkingOut.value = true;

        const requestData = {
          reservation_id: activeReservation.value.id,
        };

        // 如果启用签名
        if (checkOutForm.useSignature && userHasPublicKey.value) {
          const privateKey = localStorage.getItem("sm2_private_key");
          if (privateKey) {
            const signature = SM2Crypto.sign(
              privateKey,
              activeReservation.value.reservation_code
            );
            requestData.signature = signature;
          }
        }

        await store.dispatch("seat/checkOut", requestData);
        
        ElMessage.success("签退成功");
        checkOutDialogVisible.value = false;
        await loadActiveReservation();
      } catch (error) {
        ElMessage.error(error.message || "签退失败");
      } finally {
        checkingOut.value = false;
      }
    };

    const handleCancel = async () => {
      try {
        cancelling.value = true;

        await store.dispatch("seat/cancelReservation", {
          id: activeReservation.value.id,
        });
        
        ElMessage.success("预约已取消");
        cancelDialogVisible.value = false;
        await loadActiveReservation();
      } catch (error) {
        ElMessage.error(error.message || "取消预约失败");
      } finally {
        cancelling.value = false;
      }
    };

    const handleDialogClose = (done) => {
      if (checkingIn.value || checkingOut.value || cancelling.value) {
        ElMessage.warning("操作进行中，请稍候...");
        return;
      }
      done();
    };

    const getStatusType = (status) => {
      switch (status) {
        case "pending":
          return "warning";
        case "checked_in":
          return "success";
        case "completed":
          return "info";
        case "cancelled":
          return "danger";
        case "timeout":
          return "danger";
        default:
          return "info";
      }
    };

    const getStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待签到";
        case "checked_in":
          return "已签到";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        case "timeout":
          return "已超时";
        default:
          return "未知";
      }
    };

    const formatDateTime = (dateString) => {
      if (!dateString) return "";
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    };

    // 生命周期
    onMounted(() => {
      loadActiveReservation();
    });

    return {
      activeReservation,
      checkingIn,
      checkingOut,
      cancelling,
      checkInDialogVisible,
      checkOutDialogVisible,
      cancelDialogVisible,
      checkInForm,
      checkOutForm,
      userHasPublicKey,
      showCheckInDialog,
      showCheckOutDialog,
      showCancelDialog,
      handleCheckIn,
      handleCheckOut,
      handleCancel,
      handleDialogClose,
      getStatusType,
      getStatusText,
      formatDateTime,
    };
  },
};
</script>

<style scoped>
.checkin-checkout-container {
  padding: 20px;
}

.main-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.active-reservation {
  margin-bottom: 20px;
}

.reservation-info {
  margin-top: 10px;
}

.reservation-info p {
  margin: 5px 0;
  color: #606266;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 10px;
  min-width: 120px;
}

.no-reservation {
  text-align: center;
  padding: 40px 0;
}

.dialog-content {
  text-align: center;
}

.seat-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;
  text-align: left;
}

.seat-info p {
  margin: 5px 0;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
