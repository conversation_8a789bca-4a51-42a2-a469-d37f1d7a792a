/**
 * WebSocket连接管理工具
 */
import { ElMessage } from "element-plus";
import store from "@/store";

class WebSocketManager {
  constructor() {
    this.connections = new Map();
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
  }

  /**
   * 创建WebSocket连接
   * @param {string} url - WebSocket URL
   * @param {string} name - 连接名称
   * @param {Object} options - 选项
   * @returns {WebSocket}
   */
  connect(url, name, options = {}) {
    if (this.connections.has(name)) {
      this.disconnect(name);
    }

    const token = localStorage.getItem("token");
    if (!token) {
      console.error("WebSocket连接失败: 缺少认证令牌");
      return null;
    }

    const ws = new WebSocket(url);
    const connectionInfo = {
      ws,
      url,
      options,
      isAuthenticated: false,
      messageQueue: [],
    };

    this.connections.set(name, connectionInfo);
    this.reconnectAttempts.set(name, 0);

    // 连接打开
    ws.onopen = () => {
      console.log(`WebSocket连接已建立: ${name}`);
      
      // 发送认证消息
      this.authenticate(name, token);
      
      if (options.onOpen) {
        options.onOpen();
      }
    };

    // 接收消息
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(name, data);
      } catch (error) {
        console.error("WebSocket消息解析失败:", error);
      }
    };

    // 连接关闭
    ws.onclose = (event) => {
      console.log(`WebSocket连接已关闭: ${name}`, event.code, event.reason);
      
      if (options.onClose) {
        options.onClose(event);
      }

      // 自动重连
      if (event.code !== 1000 && this.shouldReconnect(name)) {
        this.scheduleReconnect(name);
      }
    };

    // 连接错误
    ws.onerror = (error) => {
      console.error(`WebSocket连接错误: ${name}`, error);
      
      if (options.onError) {
        options.onError(error);
      }
    };

    return ws;
  }

  /**
   * 认证WebSocket连接
   * @param {string} name - 连接名称
   * @param {string} token - JWT令牌
   */
  authenticate(name, token) {
    const connectionInfo = this.connections.get(name);
    if (!connectionInfo) return;

    const authMessage = {
      type: "authenticate",
      token: token,
    };

    this.send(name, authMessage);
  }

  /**
   * 发送消息
   * @param {string} name - 连接名称
   * @param {Object} message - 消息对象
   */
  send(name, message) {
    const connectionInfo = this.connections.get(name);
    if (!connectionInfo) {
      console.error(`WebSocket连接不存在: ${name}`);
      return false;
    }

    const { ws, isAuthenticated, messageQueue } = connectionInfo;

    if (ws.readyState === WebSocket.OPEN) {
      if (message.type === "authenticate" || isAuthenticated) {
        ws.send(JSON.stringify(message));
        return true;
      } else {
        // 如果未认证，将消息加入队列
        messageQueue.push(message);
        return false;
      }
    } else {
      console.error(`WebSocket连接未就绪: ${name}`);
      return false;
    }
  }

  /**
   * 处理接收到的消息
   * @param {string} name - 连接名称
   * @param {Object} data - 消息数据
   */
  handleMessage(name, data) {
    const connectionInfo = this.connections.get(name);
    if (!connectionInfo) return;

    const { options, messageQueue } = connectionInfo;

    switch (data.type) {
      case "connection_established":
        console.log(`WebSocket连接建立确认: ${name}`);
        break;

      case "auth_success":
        console.log(`WebSocket认证成功: ${name}`);
        connectionInfo.isAuthenticated = true;
        
        // 发送队列中的消息
        while (messageQueue.length > 0) {
          const queuedMessage = messageQueue.shift();
          this.send(name, queuedMessage);
        }
        
        if (options.onAuthenticated) {
          options.onAuthenticated(data);
        }
        break;

      case "auth_error":
        console.error(`WebSocket认证失败: ${name}`, data.message);
        ElMessage.error(`WebSocket认证失败: ${data.message}`);
        break;

      case "seat_status_update":
        this.handleSeatStatusUpdate(data.data);
        break;

      case "reservation_update":
        this.handleReservationUpdate(data.data);
        break;

      case "reservation_reminder":
        this.handleReservationReminder(data.data);
        break;

      case "system_notification":
        this.handleSystemNotification(data.data);
        break;

      case "error":
        console.error(`WebSocket错误: ${name}`, data.message);
        ElMessage.error(data.message);
        break;

      default:
        if (options.onMessage) {
          options.onMessage(data);
        }
        break;
    }
  }

  /**
   * 处理座位状态更新
   * @param {Object} seatData - 座位数据
   */
  handleSeatStatusUpdate(seatData) {
    // 更新Vuex中的座位状态
    store.dispatch("seat/updateSeatStatus", seatData);
  }

  /**
   * 处理预约更新
   * @param {Object} reservationData - 预约数据
   */
  handleReservationUpdate(reservationData) {
    // 更新Vuex中的预约状态
    store.dispatch("seat/updateReservationStatus", reservationData);
  }

  /**
   * 处理预约提醒
   * @param {Object} reminderData - 提醒数据
   */
  handleReservationReminder(reminderData) {
    ElMessage({
      title: "预约提醒",
      message: `您的预约即将开始，请及时签到`,
      type: "warning",
      duration: 10000,
    });
  }

  /**
   * 处理系统通知
   * @param {Object} notificationData - 通知数据
   */
  handleSystemNotification(notificationData) {
    ElMessage({
      title: notificationData.title,
      message: notificationData.message,
      type: notificationData.type || "info",
      duration: 5000,
    });
  }

  /**
   * 订阅座位状态
   * @param {string} name - 连接名称
   * @param {number} roomId - 房间ID
   */
  subscribeSeatStatus(name, roomId) {
    const message = {
      type: "subscribe",
      channel: "seat_status",
      room_id: roomId,
    };
    this.send(name, message);
  }

  /**
   * 取消订阅座位状态
   * @param {string} name - 连接名称
   * @param {number} roomId - 房间ID
   */
  unsubscribeSeatStatus(name, roomId) {
    const message = {
      type: "unsubscribe",
      channel: "seat_status",
      room_id: roomId,
    };
    this.send(name, message);
  }

  /**
   * 断开WebSocket连接
   * @param {string} name - 连接名称
   */
  disconnect(name) {
    const connectionInfo = this.connections.get(name);
    if (connectionInfo) {
      connectionInfo.ws.close(1000, "主动断开连接");
      this.connections.delete(name);
      this.reconnectAttempts.delete(name);
    }
  }

  /**
   * 断开所有WebSocket连接
   */
  disconnectAll() {
    for (const name of this.connections.keys()) {
      this.disconnect(name);
    }
  }

  /**
   * 检查是否应该重连
   * @param {string} name - 连接名称
   * @returns {boolean}
   */
  shouldReconnect(name) {
    const attempts = this.reconnectAttempts.get(name) || 0;
    return attempts < this.maxReconnectAttempts;
  }

  /**
   * 安排重连
   * @param {string} name - 连接名称
   */
  scheduleReconnect(name) {
    const connectionInfo = this.connections.get(name);
    if (!connectionInfo) return;

    const attempts = this.reconnectAttempts.get(name) || 0;
    this.reconnectAttempts.set(name, attempts + 1);

    console.log(`安排WebSocket重连: ${name}, 尝试次数: ${attempts + 1}`);

    setTimeout(() => {
      if (this.connections.has(name)) {
        this.connect(connectionInfo.url, name, connectionInfo.options);
      }
    }, this.reconnectInterval * (attempts + 1));
  }

  /**
   * 获取连接状态
   * @param {string} name - 连接名称
   * @returns {number|null}
   */
  getConnectionState(name) {
    const connectionInfo = this.connections.get(name);
    return connectionInfo ? connectionInfo.ws.readyState : null;
  }

  /**
   * 检查连接是否已认证
   * @param {string} name - 连接名称
   * @returns {boolean}
   */
  isAuthenticated(name) {
    const connectionInfo = this.connections.get(name);
    return connectionInfo ? connectionInfo.isAuthenticated : false;
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager();

export default wsManager;
