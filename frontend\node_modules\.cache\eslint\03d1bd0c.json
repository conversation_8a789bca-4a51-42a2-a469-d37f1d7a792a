[{"C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\main.js": "1", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\App.vue": "2", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\index.js": "3", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\router\\index.js": "4", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\NotFound.vue": "5", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Help.vue": "6", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue": "7", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\CreditRecords.vue": "8", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue": "9", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\MyReservations.vue": "10", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue": "11", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue": "12", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue": "13", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\RoomList.vue": "14", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\UserProfile.vue": "15", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatMap.vue": "16", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatReservation.vue": "17", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Layout.vue": "18", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\user.js": "19", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\seat.js": "20", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\user.js": "21", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\seat.js": "22", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\http.js": "23", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Header.vue": "24", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Sidebar.vue": "25", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Footer.vue": "26", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\crypto.js": "27", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\mock\\data.js": "28", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\CheckInOut.vue": "29", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\test\\WebSocketTest.vue": "30", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\websocket.js": "31"}, {"size": 519, "mtime": 1747826136084, "results": "32", "hashOfConfig": "33"}, {"size": 364, "mtime": 1747826136084, "results": "34", "hashOfConfig": "33"}, {"size": 936, "mtime": 1747822521897, "results": "35", "hashOfConfig": "33"}, {"size": 4623, "mtime": 1749441723031, "results": "36", "hashOfConfig": "33"}, {"size": 549, "mtime": 1747825407223, "results": "37", "hashOfConfig": "33"}, {"size": 4134, "mtime": 1747825313033, "results": "38", "hashOfConfig": "33"}, {"size": 12446, "mtime": 1749441589996, "results": "39", "hashOfConfig": "33"}, {"size": 9145, "mtime": 1747824623676, "results": "40", "hashOfConfig": "33"}, {"size": 3545, "mtime": 1747825382947, "results": "41", "hashOfConfig": "33"}, {"size": 20132, "mtime": 1747824623676, "results": "42", "hashOfConfig": "33"}, {"size": 10847, "mtime": 1747824623676, "results": "43", "hashOfConfig": "33"}, {"size": 15826, "mtime": 1747991382301, "results": "44", "hashOfConfig": "33"}, {"size": 7687, "mtime": 1747991393641, "results": "45", "hashOfConfig": "33"}, {"size": 11862, "mtime": 1747834257101, "results": "46", "hashOfConfig": "33"}, {"size": 18735, "mtime": 1747826659267, "results": "47", "hashOfConfig": "33"}, {"size": 19482, "mtime": 1749441559604, "results": "48", "hashOfConfig": "33"}, {"size": 21361, "mtime": 1747824623676, "results": "49", "hashOfConfig": "33"}, {"size": 997, "mtime": 1747833920349, "results": "50", "hashOfConfig": "33"}, {"size": 7974, "mtime": 1747822522023, "results": "51", "hashOfConfig": "33"}, {"size": 10408, "mtime": 1749442368034, "results": "52", "hashOfConfig": "33"}, {"size": 1247, "mtime": 1747822521337, "results": "53", "hashOfConfig": "33"}, {"size": 1727, "mtime": 1749441459231, "results": "54", "hashOfConfig": "33"}, {"size": 4835, "mtime": 1749441508096, "results": "55", "hashOfConfig": "33"}, {"size": 4251, "mtime": 1747834257101, "results": "56", "hashOfConfig": "33"}, {"size": 3086, "mtime": 1747825357959, "results": "57", "hashOfConfig": "33"}, {"size": 1088, "mtime": 1747824623676, "results": "58", "hashOfConfig": "33"}, {"size": 7179, "mtime": 1747822522071, "results": "59", "hashOfConfig": "33"}, {"size": 8820, "mtime": 1749442368035, "results": "60", "hashOfConfig": "33"}, {"size": 13623, "mtime": 1749442368034, "results": "61", "hashOfConfig": "33"}, {"size": 10819, "mtime": 1749442368035, "results": "62", "hashOfConfig": "33"}, {"size": 8499, "mtime": 1749442368035, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, "q2ekvd", {"filePath": "67", "messages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "96", "messages": "97", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "112"}, {"filePath": "113", "messages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "115", "messages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "117", "messages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\main.js", [], [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\App.vue", [], [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\index.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\router\\index.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\NotFound.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Help.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\CreditRecords.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\MyReservations.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\RoomList.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\UserProfile.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatMap.vue", ["129", "130", "131"], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatReservation.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Layout.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\user.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\seat.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\user.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\seat.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\http.js", ["132"], "import axios from \"axios\";\nimport { ElMessage } from \"element-plus\";\nimport router from \"@/router\";\nimport { SM4Crypto } from \"@/utils/crypto\";\n\n// 创建axios实例\nconst http = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || \"/api\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(\n  (config) => {\n    // 从localStorage获取token\n    const token = localStorage.getItem(\"token\");\n\n    // 如果有token，则添加到请求头\n    if (token) {\n      config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n\n    // 检查是否需要加密请求数据\n    if (config.encrypt && config.data) {\n      try {\n        // 动态导入加密工具\n        const { default: CryptoUtils } = await import(\"@/utils/cryptoUtils\");\n\n        // 加密请求数据\n        const encryptedPackage = await CryptoUtils.encryptRequest(config.data);\n\n        // 替换请求数据\n        config.data = encryptedPackage;\n\n        // 添加加密标记\n        config.headers[\"X-Encrypted\"] = \"true\";\n      } catch (error) {\n        console.error(\"请求数据加密失败:\", error);\n        // 如果加密失败，继续发送原始数据\n      }\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nhttp.interceptors.response.use(\n  async (response) => {\n    // 检查响应是否加密\n    if (response.headers[\"x-encrypted\"] === \"true\" && response.data) {\n      try {\n        // 动态导入加密工具\n        const { default: CryptoUtils } = await import(\"@/utils/cryptoUtils\");\n\n        // 获取客户端私钥\n        const privateKey = localStorage.getItem(\"sm2_private_key\");\n        if (!privateKey) {\n          console.warn(\"未找到客户端私钥，无法解密响应\");\n        } else {\n          // 解密响应数据\n          const decryptedData = CryptoUtils.decryptResponse(\n            response.data,\n            privateKey\n          );\n          response.data = decryptedData;\n        }\n      } catch (error) {\n        console.error(\"响应解密失败:\", error);\n        // 如果解密失败，继续使用原始数据\n      }\n    }\n\n    // 检查响应状态\n    if (response.data && response.data.code !== undefined) {\n      if (response.data.code === 0) {\n        // 成功响应，返回数据部分\n        return response.data.data || response.data;\n      } else {\n        // 业务错误\n        const error = new Error(response.data.message || \"请求失败\");\n        error.code = response.data.code;\n        return Promise.reject(error);\n      }\n    }\n\n    // 直接返回响应数据\n    return response.data;\n  },\n  (error) => {\n    if (error.response) {\n      // 处理响应错误\n      switch (error.response.status) {\n        case 401:\n          // 未授权，清除token并跳转到登录页\n          localStorage.removeItem(\"token\");\n          localStorage.removeItem(\"userInfo\");\n\n          // 如果不是登录页，则跳转到登录页\n          if (router.currentRoute.value.path !== \"/login\") {\n            ElMessage.error(\"登录已过期，请重新登录\");\n            router.push(\"/login\");\n          }\n          break;\n\n        case 403:\n          // 禁止访问\n          ElMessage.error(\"没有权限访问该资源\");\n          break;\n\n        case 404:\n          // 资源不存在\n          ElMessage.error(\"请求的资源不存在\");\n          break;\n\n        case 500:\n          // 服务器错误\n          ElMessage.error(\"服务器错误，请稍后重试\");\n          break;\n\n        default:\n          // 其他错误\n          if (error.response.data && error.response.data.message) {\n            ElMessage.error(error.response.data.message);\n          } else {\n            ElMessage.error(\"请求失败，请稍后重试\");\n          }\n      }\n    } else if (error.request) {\n      // 请求发送但没有收到响应\n      ElMessage.error(\"网络错误，请检查网络连接\");\n    } else {\n      // 请求配置错误\n      ElMessage.error(\"请求配置错误\");\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 导出请求方法\nexport default {\n  // GET请求\n  get(url, params = {}, config = {}) {\n    return http.get(url, { params, ...config });\n  },\n\n  // POST请求\n  post(url, data = {}, config = {}) {\n    return http.post(url, data, config);\n  },\n\n  // PUT请求\n  put(url, data = {}, config = {}) {\n    return http.put(url, data, config);\n  },\n\n  // DELETE请求\n  delete(url, config = {}) {\n    return http.delete(url, config);\n  },\n\n  // 加密POST请求\n  encryptedPost(url, data = {}, config = {}) {\n    return http.post(url, data, { ...config, encrypt: true });\n  },\n\n  // 加密PUT请求\n  encryptedPut(url, data = {}, config = {}) {\n    return http.put(url, data, { ...config, encrypt: true });\n  },\n};\n", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Header.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Sidebar.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Footer.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\crypto.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\mock\\data.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\CheckInOut.vue", ["133"], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\test\\WebSocketTest.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\websocket.js", ["134"], {"ruleId": "135", "severity": 2, "message": "136", "line": 525, "column": 12, "nodeType": "137", "messageId": "138", "endLine": 525, "endColumn": 18}, {"ruleId": "135", "severity": 2, "message": "136", "line": 527, "column": 52, "nodeType": "137", "messageId": "138", "endLine": 527, "endColumn": 58}, {"ruleId": "135", "severity": 2, "message": "136", "line": 531, "column": 55, "nodeType": "137", "messageId": "138", "endLine": 531, "endColumn": 61}, {"ruleId": null, "fatal": true, "severity": 2, "message": "139", "line": 30, "column": 41}, {"ruleId": "140", "severity": 2, "message": "141", "line": 236, "column": 21, "nodeType": "137", "messageId": "142", "endLine": 236, "endColumn": 33}, {"ruleId": "140", "severity": 2, "message": "143", "line": 226, "column": 29, "nodeType": "137", "messageId": "142", "endLine": 226, "endColumn": 41}, "no-undef", "'roomId' is not defined.", "Identifier", "undef", "Parsing error: Unexpected reserved word 'await'. (30:41)", "no-unused-vars", "'ElMessageBox' is defined but never used.", "unusedVar", "'reminderData' is defined but never used."]