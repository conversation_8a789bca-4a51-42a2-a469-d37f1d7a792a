[{"C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\main.js": "1", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\App.vue": "2", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\index.js": "3", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\router\\index.js": "4", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\NotFound.vue": "5", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Help.vue": "6", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue": "7", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\CreditRecords.vue": "8", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue": "9", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\MyReservations.vue": "10", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue": "11", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue": "12", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue": "13", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\RoomList.vue": "14", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\UserProfile.vue": "15", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatMap.vue": "16", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatReservation.vue": "17", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Layout.vue": "18", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\user.js": "19", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\seat.js": "20", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\user.js": "21", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\seat.js": "22", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\http.js": "23", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Header.vue": "24", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Sidebar.vue": "25", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Footer.vue": "26", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\crypto.js": "27", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\mock\\data.js": "28", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\CheckInOut.vue": "29", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\test\\WebSocketTest.vue": "30", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\websocket.js": "31"}, {"size": 519, "mtime": 1747826136084, "results": "32", "hashOfConfig": "33"}, {"size": 364, "mtime": 1747826136084, "results": "34", "hashOfConfig": "33"}, {"size": 936, "mtime": 1747822521897, "results": "35", "hashOfConfig": "33"}, {"size": 4623, "mtime": 1749441723031, "results": "36", "hashOfConfig": "33"}, {"size": 549, "mtime": 1747825407223, "results": "37", "hashOfConfig": "33"}, {"size": 4134, "mtime": 1747825313033, "results": "38", "hashOfConfig": "33"}, {"size": 12446, "mtime": 1749441589996, "results": "39", "hashOfConfig": "33"}, {"size": 9145, "mtime": 1747824623676, "results": "40", "hashOfConfig": "33"}, {"size": 3545, "mtime": 1747825382947, "results": "41", "hashOfConfig": "33"}, {"size": 20132, "mtime": 1747824623676, "results": "42", "hashOfConfig": "33"}, {"size": 10847, "mtime": 1747824623676, "results": "43", "hashOfConfig": "33"}, {"size": 15826, "mtime": 1747991382301, "results": "44", "hashOfConfig": "33"}, {"size": 7687, "mtime": 1747991393641, "results": "45", "hashOfConfig": "33"}, {"size": 11862, "mtime": 1747834257101, "results": "46", "hashOfConfig": "33"}, {"size": 18735, "mtime": 1747826659267, "results": "47", "hashOfConfig": "33"}, {"size": 19482, "mtime": 1749441559604, "results": "48", "hashOfConfig": "33"}, {"size": 21361, "mtime": 1747824623676, "results": "49", "hashOfConfig": "33"}, {"size": 997, "mtime": 1747833920349, "results": "50", "hashOfConfig": "33"}, {"size": 7974, "mtime": 1747822522023, "results": "51", "hashOfConfig": "33"}, {"size": 10408, "mtime": 1749442368034, "results": "52", "hashOfConfig": "33"}, {"size": 1247, "mtime": 1747822521337, "results": "53", "hashOfConfig": "33"}, {"size": 1727, "mtime": 1749441459231, "results": "54", "hashOfConfig": "33"}, {"size": 4835, "mtime": 1749441508096, "results": "55", "hashOfConfig": "33"}, {"size": 4251, "mtime": 1747834257101, "results": "56", "hashOfConfig": "33"}, {"size": 3086, "mtime": 1747825357959, "results": "57", "hashOfConfig": "33"}, {"size": 1088, "mtime": 1747824623676, "results": "58", "hashOfConfig": "33"}, {"size": 7179, "mtime": 1747822522071, "results": "59", "hashOfConfig": "33"}, {"size": 8820, "mtime": 1749442368035, "results": "60", "hashOfConfig": "33"}, {"size": 13623, "mtime": 1749442368034, "results": "61", "hashOfConfig": "33"}, {"size": 10819, "mtime": 1749442368035, "results": "62", "hashOfConfig": "33"}, {"size": 8499, "mtime": 1749442368035, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, "q2ekvd", {"filePath": "67", "messages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "96", "messages": "97", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "98"}, {"filePath": "99", "messages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "101", "messages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "103", "messages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "105", "messages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "109", "messages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "113"}, {"filePath": "114", "messages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "116", "messages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "118", "messages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "69"}, {"filePath": "120", "messages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "66"}, {"filePath": "122", "messages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "126"}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "131"}, "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\main.js", [], [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\App.vue", [], [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\index.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\router\\index.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\NotFound.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Help.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\CreditRecords.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\MyReservations.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\ReservationDetail.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Register.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\auth\\Login.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\RoomList.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\UserProfile.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatMap.vue", ["132", "133", "134"], "<template>\n  <div class=\"seat-map\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2 v-if=\"room\">{{ room.name }} - 座位图</h2>\n      </div>\n\n      <div class=\"header-right\">\n        <el-select\n          v-model=\"selectedDate\"\n          placeholder=\"选择日期\"\n          @change=\"loadSeats\"\n        >\n          <el-option\n            v-for=\"date in dateOptions\"\n            :key=\"date.value\"\n            :label=\"date.label\"\n            :value=\"date.value\"\n          />\n        </el-select>\n\n        <el-button type=\"primary\" @click=\"loadSeats\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <template v-else>\n      <el-card class=\"room-info-card\" shadow=\"never\">\n        <div class=\"room-info\">\n          <div class=\"info-item\">\n            <el-icon><Location /></el-icon>\n            <span>位置: {{ room?.location }}</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><Clock /></el-icon>\n            <span>\n              开放时间: {{ formatTime(room?.open_time) }} -\n              {{ formatTime(room?.close_time) }}\n            </span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><User /></el-icon>\n            <span>容量: {{ room?.capacity }}座</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><InfoFilled /></el-icon>\n            <span>可用座位: {{ availableSeats }}/{{ room?.capacity }}</span>\n          </div>\n        </div>\n\n        <div class=\"seat-legend\">\n          <div class=\"legend-item\">\n            <div class=\"seat-icon available\"></div>\n            <span>可用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon occupied\"></div>\n            <span>已占用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon disabled\"></div>\n            <span>禁用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon power-outlet\"></div>\n            <span>电源</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon window\"></div>\n            <span>靠窗</span>\n          </div>\n        </div>\n      </el-card>\n\n      <div class=\"map-container\">\n        <div class=\"seat-filter\">\n          <el-checkbox v-model=\"filters.powerOutlet\" @change=\"applyFilters\">\n            只看有电源的座位\n          </el-checkbox>\n\n          <el-checkbox v-model=\"filters.windowSeat\" @change=\"applyFilters\">\n            只看靠窗座位\n          </el-checkbox>\n\n          <el-checkbox v-model=\"filters.availableOnly\" @change=\"applyFilters\">\n            只看可用座位\n          </el-checkbox>\n        </div>\n\n        <div class=\"seat-grid\" :style=\"gridStyle\">\n          <div\n            v-for=\"seat in filteredSeats\"\n            :key=\"seat.id\"\n            class=\"seat\"\n            :class=\"getSeatClasses(seat)\"\n            :style=\"getSeatStyle(seat)\"\n            @click=\"selectSeat(seat)\"\n          >\n            <div class=\"seat-number\">{{ seat.seat_number }}</div>\n            <div class=\"seat-icons\">\n              <el-icon v-if=\"seat.is_power_outlet\" class=\"power-icon\"\n                ><Lightning\n              /></el-icon>\n              <el-icon v-if=\"seat.is_window_seat\" class=\"window-icon\"\n                ><Sunny\n              /></el-icon>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 座位详情对话框 -->\n      <el-dialog\n        v-model=\"seatDialogVisible\"\n        :title=\"`座位详情 - ${selectedSeat?.seat_number}`\"\n        width=\"500px\"\n      >\n        <div v-if=\"selectedSeat\" class=\"seat-detail\">\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"座位编号\">\n              {{ selectedSeat.seat_number }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"位置\">\n              {{ `${selectedSeat.row}排${selectedSeat.column}列` }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"状态\">\n              <el-tag :type=\"getSeatStatusType(selectedSeat.status)\">\n                {{ getSeatStatusText(selectedSeat.status) }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"设施\">\n              <el-tag\n                v-if=\"selectedSeat.is_power_outlet\"\n                type=\"success\"\n                effect=\"plain\"\n              >\n                有电源\n              </el-tag>\n              <el-tag\n                v-if=\"selectedSeat.is_window_seat\"\n                type=\"success\"\n                effect=\"plain\"\n                >靠窗</el-tag\n              >\n              <span\n                v-if=\"\n                  !selectedSeat.is_power_outlet && !selectedSeat.is_window_seat\n                \"\n              >\n                无特殊设施\n              </span>\n            </el-descriptions-item>\n          </el-descriptions>\n\n          <div\n            v-if=\"selectedSeat.current_reservation\"\n            class=\"current-reservation\"\n          >\n            <h4>当前预约信息</h4>\n            <el-descriptions :column=\"1\" border>\n              <el-descriptions-item label=\"预约状态\">\n                <el-tag\n                  :type=\"\n                    getReservationStatusType(\n                      selectedSeat.current_reservation.status\n                    )\n                  \"\n                >\n                  {{\n                    getReservationStatusText(\n                      selectedSeat.current_reservation.status\n                    )\n                  }}\n                </el-tag>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"开始时间\">\n                {{\n                  formatDateTime(selectedSeat.current_reservation.start_time)\n                }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"结束时间\">\n                {{ formatDateTime(selectedSeat.current_reservation.end_time) }}\n              </el-descriptions-item>\n            </el-descriptions>\n          </div>\n\n          <div v-if=\"selectedSeat.status === 'available'\" class=\"seat-actions\">\n            <el-button type=\"primary\" @click=\"reserveSeat\"\n              >预约此座位</el-button\n            >\n          </div>\n        </div>\n      </el-dialog>\n    </template>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted, reactive } from \"vue\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport wsManager from \"@/utils/websocket\";\nimport {\n  ArrowLeft,\n  Refresh,\n  Location,\n  Clock,\n  User,\n  InfoFilled,\n  Lightning,\n  Sunny,\n} from \"@element-plus/icons-vue\";\n// 导入模拟数据\nimport { rooms as mockRooms, generateSeats } from \"@/mock/data\";\n\nexport default {\n  name: \"SeatMap\",\n  setup() {\n    const route = useRoute();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const room = ref(null);\n    const seats = ref([]);\n    const selectedDate = ref(formatDateForSelect(new Date()));\n    const seatDialogVisible = ref(false);\n    const selectedSeat = ref(null);\n\n    // 过滤条件\n    const filters = reactive({\n      powerOutlet: false,\n      windowSeat: false,\n      availableOnly: false,\n    });\n\n    // 日期选项\n    const dateOptions = computed(() => {\n      const options = [];\n      const today = new Date();\n\n      for (let i = 0; i < 7; i++) {\n        const date = new Date();\n        date.setDate(today.getDate() + i);\n\n        options.push({\n          value: formatDateForSelect(date),\n          label: formatDateForDisplay(date),\n        });\n      }\n\n      return options;\n    });\n\n    // 可用座位数量\n    const availableSeats = computed(() => {\n      return seats.value.filter((seat) => seat.status === \"available\").length;\n    });\n\n    // 过滤后的座位\n    const filteredSeats = computed(() => {\n      return seats.value.filter((seat) => {\n        if (filters.powerOutlet && !seat.is_power_outlet) return false;\n        if (filters.windowSeat && !seat.is_window_seat) return false;\n        if (filters.availableOnly && seat.status !== \"available\") return false;\n        return true;\n      });\n    });\n\n    // 座位网格样式\n    const gridStyle = computed(() => {\n      if (!room.value) return {};\n\n      // 固定6x6网格\n      return {\n        gridTemplateRows: `repeat(6, 60px)`,\n        gridTemplateColumns: `repeat(6, 60px)`,\n      };\n    });\n\n    // 加载自习室和座位信息\n    const loadRoomAndSeats = async () => {\n      try {\n        loading.value = true;\n\n        const roomId = parseInt(route.query.roomId);\n        if (!roomId) {\n          ElMessage.error(\"缺少自习室ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 从模拟数据中查找自习室\n        const roomData = mockRooms.find((r) => r.id === roomId);\n        if (!roomData) {\n          ElMessage.error(\"未找到自习室信息\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 转换数据格式\n        room.value = {\n          ...roomData,\n          open_time: roomData.openTime,\n          close_time: roomData.closeTime,\n          available_seats: roomData.availableSeats,\n        };\n\n        // 加载座位信息\n        await loadSeats();\n      } catch (error) {\n        ElMessage.error(\"加载自习室信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载座位信息\n    const loadSeats = async () => {\n      try {\n        loading.value = true;\n\n        const roomId = parseInt(route.query.roomId);\n        if (!roomId) return;\n\n        // 模拟API请求延迟\n        await new Promise((resolve) => setTimeout(resolve, 500));\n\n        // 使用模拟数据生成座位\n        const seatsData = generateSeats(roomId);\n\n        // 转换数据格式\n        seats.value = seatsData.map((seat) => ({\n          ...seat,\n          is_power_outlet: seat.isPowerOutlet,\n          is_window_seat: seat.isWindowSeat,\n          seat_number: seat.seatNumber,\n        }));\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 应用过滤器\n    const applyFilters = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 选择座位\n    const selectSeat = (seat) => {\n      selectedSeat.value = seat;\n      seatDialogVisible.value = true;\n    };\n\n    // 预约座位\n    const reserveSeat = () => {\n      if (!selectedSeat.value) return;\n\n      router.push({\n        path: \"/seat/reservation\",\n        query: {\n          seatId: selectedSeat.value.id,\n          date: selectedDate.value,\n        },\n      });\n    };\n\n    // 获取座位类名\n    const getSeatClasses = (seat) => {\n      return {\n        \"seat-available\": seat.status === \"available\",\n        \"seat-occupied\": seat.status === \"occupied\",\n        \"seat-disabled\": seat.status === \"disabled\",\n        \"seat-power\": seat.is_power_outlet,\n        \"seat-window\": seat.is_window_seat,\n      };\n    };\n\n    // 获取座位样式\n    const getSeatStyle = (seat) => {\n      return {\n        gridRow: `${seat.row} / span 1`,\n        gridColumn: `${seat.column} / span 1`,\n      };\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )}`;\n    }\n\n    // 格式化日期（用于显示）\n    function formatDateForDisplay(date) {\n      const today = new Date();\n      const tomorrow = new Date();\n      tomorrow.setDate(today.getDate() + 1);\n\n      if (date.toDateString() === today.toDateString()) {\n        return \"今天\";\n      } else if (date.toDateString() === tomorrow.toDateString()) {\n        return \"明天\";\n      } else {\n        const weekdays = [\n          \"周日\",\n          \"周一\",\n          \"周二\",\n          \"周三\",\n          \"周四\",\n          \"周五\",\n          \"周六\",\n        ];\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${\n          weekdays[date.getDay()]\n        }`;\n      }\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // WebSocket连接管理\n    const connectWebSocket = () => {\n      if (!roomId.value) return;\n\n      const wsUrl = `ws://localhost:8000/ws/seat/${roomId.value}/`;\n      wsManager.connect(wsUrl, \"seatStatus\", {\n        onAuthenticated: () => {\n          console.log(\"WebSocket认证成功，订阅座位状态\");\n          wsManager.subscribeSeatStatus(\"seatStatus\", roomId.value);\n        },\n        onMessage: (data) => {\n          console.log(\"收到WebSocket消息:\", data);\n        },\n        onClose: () => {\n          console.log(\"WebSocket连接已关闭\");\n        },\n        onError: (error) => {\n          console.error(\"WebSocket连接错误:\", error);\n        },\n      });\n    };\n\n    const disconnectWebSocket = () => {\n      wsManager.disconnect(\"seatStatus\");\n    };\n\n    onMounted(() => {\n      loadRoomAndSeats();\n      connectWebSocket();\n    });\n\n    onUnmounted(() => {\n      disconnectWebSocket();\n    });\n\n    return {\n      loading,\n      room,\n      seats,\n      selectedDate,\n      dateOptions,\n      filters,\n      seatDialogVisible,\n      selectedSeat,\n      availableSeats,\n      filteredSeats,\n      gridStyle,\n      loadSeats,\n      applyFilters,\n      selectSeat,\n      reserveSeat,\n      getSeatClasses,\n      getSeatStyle,\n      getSeatStatusType,\n      getSeatStatusText,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatTime,\n      formatDateTime,\n      ArrowLeft,\n      Refresh,\n      Location,\n      Clock,\n      User,\n      InfoFilled,\n      Lightning,\n      Sunny,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.seat-map {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n\n  .header-right {\n    display: flex;\n    gap: 10px;\n  }\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.room-info-card {\n  margin-bottom: 20px;\n\n  .room-info {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n    margin-bottom: 15px;\n\n    .info-item {\n      display: flex;\n      align-items: center;\n\n      .el-icon {\n        margin-right: 8px;\n        color: #909399;\n      }\n    }\n  }\n\n  .seat-legend {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 15px;\n\n    .legend-item {\n      display: flex;\n      align-items: center;\n\n      .seat-icon {\n        width: 20px;\n        height: 20px;\n        border-radius: 4px;\n        margin-right: 5px;\n\n        &.available {\n          background-color: #67c23a;\n        }\n\n        &.occupied {\n          background-color: #f56c6c;\n        }\n\n        &.disabled {\n          background-color: #909399;\n        }\n\n        &.power-outlet {\n          background-color: #fff;\n          border: 1px solid #67c23a;\n          position: relative;\n\n          &::after {\n            content: \"⚡\";\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            font-size: 12px;\n          }\n        }\n\n        &.window {\n          background-color: #fff;\n          border: 1px solid #409eff;\n          position: relative;\n\n          &::after {\n            content: \"☀\";\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n\n.map-container {\n  background-color: #fff;\n  border-radius: 4px;\n  padding: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n\n  .seat-filter {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n  }\n\n  .seat-grid {\n    display: grid;\n    gap: 10px;\n    justify-content: center;\n    margin-top: 20px;\n\n    .seat {\n      width: 50px;\n      height: 50px;\n      border-radius: 4px;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      cursor: pointer;\n      transition: transform 0.2s;\n      position: relative;\n\n      &:hover {\n        transform: scale(1.1);\n        z-index: 1;\n      }\n\n      &.seat-available {\n        background-color: #67c23a;\n        color: #fff;\n      }\n\n      &.seat-occupied {\n        background-color: #f56c6c;\n        color: #fff;\n      }\n\n      &.seat-disabled {\n        background-color: #909399;\n        color: #fff;\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n        }\n      }\n\n      .seat-number {\n        font-weight: bold;\n        font-size: 14px;\n      }\n\n      .seat-icons {\n        display: flex;\n        gap: 2px;\n        margin-top: 2px;\n\n        .power-icon,\n        .window-icon {\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n\n.seat-detail {\n  .current-reservation {\n    margin-top: 20px;\n  }\n\n  .seat-actions {\n    margin-top: 20px;\n    text-align: center;\n  }\n}\n</style>\n", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatReservation.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Layout.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\user.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\store\\modules\\seat.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\user.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\seat.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\api\\http.js", ["135"], "import axios from \"axios\";\nimport { ElMessage } from \"element-plus\";\nimport router from \"@/router\";\nimport { SM4Crypto } from \"@/utils/crypto\";\n\n// 创建axios实例\nconst http = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || \"/api\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(\n  (config) => {\n    // 从localStorage获取token\n    const token = localStorage.getItem(\"token\");\n\n    // 如果有token，则添加到请求头\n    if (token) {\n      config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n\n    // 检查是否需要加密请求数据\n    if (config.encrypt && config.data) {\n      try {\n        // 动态导入加密工具\n        const { default: CryptoUtils } = await import(\"@/utils/cryptoUtils\");\n\n        // 加密请求数据\n        const encryptedPackage = await CryptoUtils.encryptRequest(config.data);\n\n        // 替换请求数据\n        config.data = encryptedPackage;\n\n        // 添加加密标记\n        config.headers[\"X-Encrypted\"] = \"true\";\n      } catch (error) {\n        console.error(\"请求数据加密失败:\", error);\n        // 如果加密失败，继续发送原始数据\n      }\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nhttp.interceptors.response.use(\n  async (response) => {\n    // 检查响应是否加密\n    if (response.headers[\"x-encrypted\"] === \"true\" && response.data) {\n      try {\n        // 动态导入加密工具\n        const { default: CryptoUtils } = await import(\"@/utils/cryptoUtils\");\n\n        // 获取客户端私钥\n        const privateKey = localStorage.getItem(\"sm2_private_key\");\n        if (!privateKey) {\n          console.warn(\"未找到客户端私钥，无法解密响应\");\n        } else {\n          // 解密响应数据\n          const decryptedData = CryptoUtils.decryptResponse(\n            response.data,\n            privateKey\n          );\n          response.data = decryptedData;\n        }\n      } catch (error) {\n        console.error(\"响应解密失败:\", error);\n        // 如果解密失败，继续使用原始数据\n      }\n    }\n\n    // 检查响应状态\n    if (response.data && response.data.code !== undefined) {\n      if (response.data.code === 0) {\n        // 成功响应，返回数据部分\n        return response.data.data || response.data;\n      } else {\n        // 业务错误\n        const error = new Error(response.data.message || \"请求失败\");\n        error.code = response.data.code;\n        return Promise.reject(error);\n      }\n    }\n\n    // 直接返回响应数据\n    return response.data;\n  },\n  (error) => {\n    if (error.response) {\n      // 处理响应错误\n      switch (error.response.status) {\n        case 401:\n          // 未授权，清除token并跳转到登录页\n          localStorage.removeItem(\"token\");\n          localStorage.removeItem(\"userInfo\");\n\n          // 如果不是登录页，则跳转到登录页\n          if (router.currentRoute.value.path !== \"/login\") {\n            ElMessage.error(\"登录已过期，请重新登录\");\n            router.push(\"/login\");\n          }\n          break;\n\n        case 403:\n          // 禁止访问\n          ElMessage.error(\"没有权限访问该资源\");\n          break;\n\n        case 404:\n          // 资源不存在\n          ElMessage.error(\"请求的资源不存在\");\n          break;\n\n        case 500:\n          // 服务器错误\n          ElMessage.error(\"服务器错误，请稍后重试\");\n          break;\n\n        default:\n          // 其他错误\n          if (error.response.data && error.response.data.message) {\n            ElMessage.error(error.response.data.message);\n          } else {\n            ElMessage.error(\"请求失败，请稍后重试\");\n          }\n      }\n    } else if (error.request) {\n      // 请求发送但没有收到响应\n      ElMessage.error(\"网络错误，请检查网络连接\");\n    } else {\n      // 请求配置错误\n      ElMessage.error(\"请求配置错误\");\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 导出请求方法\nexport default {\n  // GET请求\n  get(url, params = {}, config = {}) {\n    return http.get(url, { params, ...config });\n  },\n\n  // POST请求\n  post(url, data = {}, config = {}) {\n    return http.post(url, data, config);\n  },\n\n  // PUT请求\n  put(url, data = {}, config = {}) {\n    return http.put(url, data, config);\n  },\n\n  // DELETE请求\n  delete(url, config = {}) {\n    return http.delete(url, config);\n  },\n\n  // 加密POST请求\n  encryptedPost(url, data = {}, config = {}) {\n    return http.post(url, data, { ...config, encrypt: true });\n  },\n\n  // 加密PUT请求\n  encryptedPut(url, data = {}, config = {}) {\n    return http.put(url, data, { ...config, encrypt: true });\n  },\n};\n", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Header.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Sidebar.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\components\\layout\\Footer.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\crypto.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\mock\\data.js", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\CheckInOut.vue", ["136"], "<template>\n  <div class=\"checkin-checkout-container\">\n    <el-card class=\"main-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>签到签退</h2>\n        </div>\n      </template>\n\n      <!-- 当前活跃预约 -->\n      <div v-if=\"activeReservation\" class=\"active-reservation\">\n        <el-alert\n          :title=\"`当前预约: ${activeReservation.seat.room.name} - ${activeReservation.seat.seat_number}`\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <template #default>\n            <div class=\"reservation-info\">\n              <p>\n                <strong>预约时间:</strong>\n                {{ formatDateTime(activeReservation.start_time) }} -\n                {{ formatDateTime(activeReservation.end_time) }}\n              </p>\n              <p>\n                <strong>状态:</strong>\n                <el-tag :type=\"getStatusType(activeReservation.status)\">\n                  {{ getStatusText(activeReservation.status) }}\n                </el-tag>\n              </p>\n              <p v-if=\"activeReservation.check_in_time\">\n                <strong>签到时间:</strong>\n                {{ formatDateTime(activeReservation.check_in_time) }}\n              </p>\n            </div>\n          </template>\n        </el-alert>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <el-button\n            v-if=\"activeReservation.status === 'pending'\"\n            type=\"primary\"\n            size=\"large\"\n            :loading=\"checkingIn\"\n            @click=\"showCheckInDialog\"\n          >\n            <el-icon><Location /></el-icon>\n            签到\n          </el-button>\n\n          <el-button\n            v-if=\"activeReservation.status === 'checked_in'\"\n            type=\"success\"\n            size=\"large\"\n            :loading=\"checkingOut\"\n            @click=\"showCheckOutDialog\"\n          >\n            <el-icon><CircleCheck /></el-icon>\n            签退\n          </el-button>\n\n          <el-button\n            v-if=\"activeReservation.status === 'pending'\"\n            type=\"danger\"\n            size=\"large\"\n            :loading=\"cancelling\"\n            @click=\"showCancelDialog\"\n          >\n            <el-icon><Close /></el-icon>\n            取消预约\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 无活跃预约 -->\n      <div v-else class=\"no-reservation\">\n        <el-empty description=\"当前没有活跃的预约\">\n          <el-button type=\"primary\" @click=\"$router.push('/seat/reservation')\">\n            去预约座位\n          </el-button>\n        </el-empty>\n      </div>\n    </el-card>\n\n    <!-- 签到对话框 -->\n    <el-dialog\n      v-model=\"checkInDialogVisible\"\n      title=\"确认签到\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认签到到以下座位？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>预约时间:</strong>\n            {{ formatDateTime(activeReservation?.start_time) }} -\n            {{ formatDateTime(activeReservation?.end_time) }}\n          </p>\n        </div>\n\n        <!-- 签名选项 -->\n        <el-form\n          v-if=\"userHasPublicKey\"\n          :model=\"checkInForm\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"使用数字签名验证身份\">\n            <el-switch\n              v-model=\"checkInForm.useSignature\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n            />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"checkInDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"checkingIn\"\n            @click=\"handleCheckIn\"\n          >\n            确认签到\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 签退对话框 -->\n    <el-dialog\n      v-model=\"checkOutDialogVisible\"\n      title=\"确认签退\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认签退以下座位？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>签到时间:</strong>\n            {{ formatDateTime(activeReservation?.check_in_time) }}\n          </p>\n        </div>\n\n        <!-- 签名选项 -->\n        <el-form\n          v-if=\"userHasPublicKey\"\n          :model=\"checkOutForm\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"使用数字签名验证身份\">\n            <el-switch\n              v-model=\"checkOutForm.useSignature\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n            />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"checkOutDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"success\"\n            :loading=\"checkingOut\"\n            @click=\"handleCheckOut\"\n          >\n            确认签退\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog\n      v-model=\"cancelDialogVisible\"\n      title=\"取消预约\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认取消以下预约？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>预约时间:</strong>\n            {{ formatDateTime(activeReservation?.start_time) }} -\n            {{ formatDateTime(activeReservation?.end_time) }}\n          </p>\n        </div>\n        <el-alert\n          title=\"注意：取消预约可能会影响您的信誉分\"\n          type=\"warning\"\n          :closable=\"false\"\n          show-icon\n        />\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">不取消</el-button>\n          <el-button type=\"danger\" :loading=\"cancelling\" @click=\"handleCancel\">\n            确认取消\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { Location, CircleCheck, Close } from \"@element-plus/icons-vue\";\nimport { SM2Crypto } from \"@/utils/crypto\";\n\nexport default {\n  name: \"CheckInOut\",\n  components: {\n    Location,\n    CircleCheck,\n    Close,\n  },\n  setup() {\n    const store = useStore();\n\n    // 响应式数据\n    const activeReservation = ref(null);\n    const checkingIn = ref(false);\n    const checkingOut = ref(false);\n    const cancelling = ref(false);\n\n    // 对话框状态\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 表单数据\n    const checkInForm = reactive({\n      useSignature: false,\n    });\n\n    const checkOutForm = reactive({\n      useSignature: false,\n    });\n\n    // 计算属性\n    const userHasPublicKey = computed(() => {\n      return store.getters[\"user/userInfo\"]?.public_key;\n    });\n\n    // 方法\n    const loadActiveReservation = async () => {\n      try {\n        const response = await store.dispatch(\"seat/getActiveReservation\");\n        activeReservation.value = response;\n      } catch (error) {\n        if (error.response?.status !== 404) {\n          ElMessage.error(\"获取活跃预约失败\");\n        }\n      }\n    };\n\n    const showCheckInDialog = () => {\n      checkInDialogVisible.value = true;\n    };\n\n    const showCheckOutDialog = () => {\n      checkOutDialogVisible.value = true;\n    };\n\n    const showCancelDialog = () => {\n      cancelDialogVisible.value = true;\n    };\n\n    const handleCheckIn = async () => {\n      try {\n        checkingIn.value = true;\n\n        const requestData = {\n          reservation_code: activeReservation.value.reservation_code,\n        };\n\n        // 如果启用签名\n        if (checkInForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(\n              privateKey,\n              activeReservation.value.reservation_code\n            );\n            requestData.signature = signature;\n          }\n        }\n\n        await store.dispatch(\"seat/checkIn\", requestData);\n\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        checkingIn.value = false;\n      }\n    };\n\n    const handleCheckOut = async () => {\n      try {\n        checkingOut.value = true;\n\n        const requestData = {\n          reservation_id: activeReservation.value.id,\n        };\n\n        // 如果启用签名\n        if (checkOutForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(\n              privateKey,\n              activeReservation.value.reservation_code\n            );\n            requestData.signature = signature;\n          }\n        }\n\n        await store.dispatch(\"seat/checkOut\", requestData);\n\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        checkingOut.value = false;\n      }\n    };\n\n    const handleCancel = async () => {\n      try {\n        cancelling.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          id: activeReservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        cancelling.value = false;\n      }\n    };\n\n    const handleDialogClose = (done) => {\n      if (checkingIn.value || checkingOut.value || cancelling.value) {\n        ElMessage.warning(\"操作进行中，请稍候...\");\n        return;\n      }\n      done();\n    };\n\n    const getStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    const getStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n\n    const formatDateTime = (dateString) => {\n      if (!dateString) return \"\";\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadActiveReservation();\n    });\n\n    return {\n      activeReservation,\n      checkingIn,\n      checkingOut,\n      cancelling,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      checkInForm,\n      checkOutForm,\n      userHasPublicKey,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      handleDialogClose,\n      getStatusType,\n      getStatusText,\n      formatDateTime,\n    };\n  },\n};\n</script>\n\n<style scoped>\n.checkin-checkout-container {\n  padding: 20px;\n}\n\n.main-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.card-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.active-reservation {\n  margin-bottom: 20px;\n}\n\n.reservation-info {\n  margin-top: 10px;\n}\n\n.reservation-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.action-buttons {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.action-buttons .el-button {\n  margin: 0 10px;\n  min-width: 120px;\n}\n\n.no-reservation {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.dialog-content {\n  text-align: center;\n}\n\n.seat-info {\n  background-color: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin: 15px 0;\n  text-align: left;\n}\n\n.seat-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n", "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\test\\WebSocketTest.vue", [], "C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\utils\\websocket.js", ["137"], "/**\n * WebSocket连接管理工具\n */\nimport { ElMessage } from \"element-plus\";\nimport store from \"@/store\";\n\nclass WebSocketManager {\n  constructor() {\n    this.connections = new Map();\n    this.reconnectAttempts = new Map();\n    this.maxReconnectAttempts = 5;\n    this.reconnectInterval = 3000;\n  }\n\n  /**\n   * 创建WebSocket连接\n   * @param {string} url - WebSocket URL\n   * @param {string} name - 连接名称\n   * @param {Object} options - 选项\n   * @returns {WebSocket}\n   */\n  connect(url, name, options = {}) {\n    if (this.connections.has(name)) {\n      this.disconnect(name);\n    }\n\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      console.error(\"WebSocket连接失败: 缺少认证令牌\");\n      return null;\n    }\n\n    const ws = new WebSocket(url);\n    const connectionInfo = {\n      ws,\n      url,\n      options,\n      isAuthenticated: false,\n      messageQueue: [],\n    };\n\n    this.connections.set(name, connectionInfo);\n    this.reconnectAttempts.set(name, 0);\n\n    // 连接打开\n    ws.onopen = () => {\n      console.log(`WebSocket连接已建立: ${name}`);\n\n      // 发送认证消息\n      this.authenticate(name, token);\n\n      if (options.onOpen) {\n        options.onOpen();\n      }\n    };\n\n    // 接收消息\n    ws.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        this.handleMessage(name, data);\n      } catch (error) {\n        console.error(\"WebSocket消息解析失败:\", error);\n      }\n    };\n\n    // 连接关闭\n    ws.onclose = (event) => {\n      console.log(`WebSocket连接已关闭: ${name}`, event.code, event.reason);\n\n      if (options.onClose) {\n        options.onClose(event);\n      }\n\n      // 自动重连\n      if (event.code !== 1000 && this.shouldReconnect(name)) {\n        this.scheduleReconnect(name);\n      }\n    };\n\n    // 连接错误\n    ws.onerror = (error) => {\n      console.error(`WebSocket连接错误: ${name}`, error);\n\n      if (options.onError) {\n        options.onError(error);\n      }\n    };\n\n    return ws;\n  }\n\n  /**\n   * 认证WebSocket连接\n   * @param {string} name - 连接名称\n   * @param {string} token - JWT令牌\n   */\n  authenticate(name, token) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n\n    const authMessage = {\n      type: \"authenticate\",\n      token: token,\n    };\n\n    this.send(name, authMessage);\n  }\n\n  /**\n   * 发送消息\n   * @param {string} name - 连接名称\n   * @param {Object} message - 消息对象\n   */\n  send(name, message) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) {\n      console.error(`WebSocket连接不存在: ${name}`);\n      return false;\n    }\n\n    const { ws, isAuthenticated, messageQueue } = connectionInfo;\n\n    if (ws.readyState === WebSocket.OPEN) {\n      if (message.type === \"authenticate\" || isAuthenticated) {\n        ws.send(JSON.stringify(message));\n        return true;\n      } else {\n        // 如果未认证，将消息加入队列\n        messageQueue.push(message);\n        return false;\n      }\n    } else {\n      console.error(`WebSocket连接未就绪: ${name}`);\n      return false;\n    }\n  }\n\n  /**\n   * 处理接收到的消息\n   * @param {string} name - 连接名称\n   * @param {Object} data - 消息数据\n   */\n  handleMessage(name, data) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n\n    const { options, messageQueue } = connectionInfo;\n\n    switch (data.type) {\n      case \"connection_established\":\n        console.log(`WebSocket连接建立确认: ${name}`);\n        break;\n\n      case \"auth_success\":\n        console.log(`WebSocket认证成功: ${name}`);\n        connectionInfo.isAuthenticated = true;\n\n        // 发送队列中的消息\n        while (messageQueue.length > 0) {\n          const queuedMessage = messageQueue.shift();\n          this.send(name, queuedMessage);\n        }\n\n        if (options.onAuthenticated) {\n          options.onAuthenticated(data);\n        }\n        break;\n\n      case \"auth_error\":\n        console.error(`WebSocket认证失败: ${name}`, data.message);\n        ElMessage.error(`WebSocket认证失败: ${data.message}`);\n        break;\n\n      case \"seat_status_update\":\n        this.handleSeatStatusUpdate(data.data);\n        break;\n\n      case \"reservation_update\":\n        this.handleReservationUpdate(data.data);\n        break;\n\n      case \"reservation_reminder\":\n        this.handleReservationReminder(data.data);\n        break;\n\n      case \"system_notification\":\n        this.handleSystemNotification(data.data);\n        break;\n\n      case \"error\":\n        console.error(`WebSocket错误: ${name}`, data.message);\n        ElMessage.error(data.message);\n        break;\n\n      default:\n        if (options.onMessage) {\n          options.onMessage(data);\n        }\n        break;\n    }\n  }\n\n  /**\n   * 处理座位状态更新\n   * @param {Object} seatData - 座位数据\n   */\n  handleSeatStatusUpdate(seatData) {\n    // 更新Vuex中的座位状态\n    store.dispatch(\"seat/updateSeatStatus\", seatData);\n  }\n\n  /**\n   * 处理预约更新\n   * @param {Object} reservationData - 预约数据\n   */\n  handleReservationUpdate(reservationData) {\n    // 更新Vuex中的预约状态\n    store.dispatch(\"seat/updateReservationStatus\", reservationData);\n  }\n\n  /**\n   * 处理预约提醒\n   * @param {Object} reminderData - 提醒数据\n   */\n  handleReservationReminder(reminderData) {\n    ElMessage({\n      title: \"预约提醒\",\n      message: `您的预约即将开始，请及时签到`,\n      type: \"warning\",\n      duration: 10000,\n    });\n  }\n\n  /**\n   * 处理系统通知\n   * @param {Object} notificationData - 通知数据\n   */\n  handleSystemNotification(notificationData) {\n    ElMessage({\n      title: notificationData.title,\n      message: notificationData.message,\n      type: notificationData.type || \"info\",\n      duration: 5000,\n    });\n  }\n\n  /**\n   * 订阅座位状态\n   * @param {string} name - 连接名称\n   * @param {number} roomId - 房间ID\n   */\n  subscribeSeatStatus(name, roomId) {\n    const message = {\n      type: \"subscribe\",\n      channel: \"seat_status\",\n      room_id: roomId,\n    };\n    this.send(name, message);\n  }\n\n  /**\n   * 取消订阅座位状态\n   * @param {string} name - 连接名称\n   * @param {number} roomId - 房间ID\n   */\n  unsubscribeSeatStatus(name, roomId) {\n    const message = {\n      type: \"unsubscribe\",\n      channel: \"seat_status\",\n      room_id: roomId,\n    };\n    this.send(name, message);\n  }\n\n  /**\n   * 断开WebSocket连接\n   * @param {string} name - 连接名称\n   */\n  disconnect(name) {\n    const connectionInfo = this.connections.get(name);\n    if (connectionInfo) {\n      connectionInfo.ws.close(1000, \"主动断开连接\");\n      this.connections.delete(name);\n      this.reconnectAttempts.delete(name);\n    }\n  }\n\n  /**\n   * 断开所有WebSocket连接\n   */\n  disconnectAll() {\n    for (const name of this.connections.keys()) {\n      this.disconnect(name);\n    }\n  }\n\n  /**\n   * 检查是否应该重连\n   * @param {string} name - 连接名称\n   * @returns {boolean}\n   */\n  shouldReconnect(name) {\n    const attempts = this.reconnectAttempts.get(name) || 0;\n    return attempts < this.maxReconnectAttempts;\n  }\n\n  /**\n   * 安排重连\n   * @param {string} name - 连接名称\n   */\n  scheduleReconnect(name) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n\n    const attempts = this.reconnectAttempts.get(name) || 0;\n    this.reconnectAttempts.set(name, attempts + 1);\n\n    console.log(`安排WebSocket重连: ${name}, 尝试次数: ${attempts + 1}`);\n\n    setTimeout(() => {\n      if (this.connections.has(name)) {\n        this.connect(connectionInfo.url, name, connectionInfo.options);\n      }\n    }, this.reconnectInterval * (attempts + 1));\n  }\n\n  /**\n   * 获取连接状态\n   * @param {string} name - 连接名称\n   * @returns {number|null}\n   */\n  getConnectionState(name) {\n    const connectionInfo = this.connections.get(name);\n    return connectionInfo ? connectionInfo.ws.readyState : null;\n  }\n\n  /**\n   * 检查连接是否已认证\n   * @param {string} name - 连接名称\n   * @returns {boolean}\n   */\n  isAuthenticated(name) {\n    const connectionInfo = this.connections.get(name);\n    return connectionInfo ? connectionInfo.isAuthenticated : false;\n  }\n}\n\n// 创建全局WebSocket管理器实例\nconst wsManager = new WebSocketManager();\n\nexport default wsManager;\n", {"ruleId": "138", "severity": 2, "message": "139", "line": 525, "column": 12, "nodeType": "140", "messageId": "141", "endLine": 525, "endColumn": 18}, {"ruleId": "138", "severity": 2, "message": "139", "line": 527, "column": 52, "nodeType": "140", "messageId": "141", "endLine": 527, "endColumn": 58}, {"ruleId": "138", "severity": 2, "message": "139", "line": 531, "column": 55, "nodeType": "140", "messageId": "141", "endLine": 531, "endColumn": 61}, {"ruleId": null, "fatal": true, "severity": 2, "message": "142", "line": 30, "column": 41}, {"ruleId": "143", "severity": 2, "message": "144", "line": 236, "column": 21, "nodeType": "140", "messageId": "145", "endLine": 236, "endColumn": 33}, {"ruleId": "143", "severity": 2, "message": "146", "line": 226, "column": 29, "nodeType": "140", "messageId": "145", "endLine": 226, "endColumn": 41}, "no-undef", "'roomId' is not defined.", "Identifier", "undef", "Parsing error: Unexpected reserved word 'await'. (30:41)", "no-unused-vars", "'ElMessageBox' is defined but never used.", "unusedVar", "'reminderData' is defined but never used."]