{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/**\n * 模拟数据\n * 用于前端开发阶段，提供静态数据以便展示界面效果\n */\n\n// 用户信息\nexport const userInfo = {\n  id: 1,\n  studentIdHash: \"20238273985\",\n  email: \"<EMAIL>\",\n  phone: \"18227789069\",\n  creditScore: 95,\n  publicKey: \"04dd2902c59dcf271e904f33cfed28ad5a5d217bb8634b50afb704540af085f7d816a05a518477931613585fc405d2dde780364ffb937253167cf0614293ebfb63\",\n  privateKey: \"95f766ab410295336506e9b784f714fcd20dc0df96c0c4d9ab7a3131e479c358\",\n  lastLogin: new Date().toISOString(),\n  createdAt: new Date().toISOString()\n};\n\n// 自习室列表\nexport const rooms = [{\n  id: 1,\n  name: \"1楼东区自习室\",\n  location: \"图书馆1楼东区\",\n  floor: 1,\n  capacity: 36,\n  availableSeats: 25,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆1楼东区自习室，环境安静，靠近入口\"\n}, {\n  id: 2,\n  name: \"1楼西区自习室\",\n  location: \"图书馆1楼西区\",\n  floor: 1,\n  capacity: 36,\n  availableSeats: 18,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆1楼西区自习室，靠近图书借阅区\"\n}, {\n  id: 3,\n  name: \"2楼东区自习室\",\n  location: \"图书馆2楼东区\",\n  floor: 2,\n  capacity: 36,\n  availableSeats: 30,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆2楼东区自习室，环境安静，靠近电梯\"\n}, {\n  id: 4,\n  name: \"2楼西区自习室\",\n  location: \"图书馆2楼西区\",\n  floor: 2,\n  capacity: 36,\n  availableSeats: 22,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆2楼西区自习室，靠近期刊阅览区\"\n}, {\n  id: 5,\n  name: \"3楼东区自习室\",\n  location: \"图书馆3楼东区\",\n  floor: 3,\n  capacity: 36,\n  availableSeats: 15,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆3楼东区自习室，环境安静，靠近电梯\"\n}, {\n  id: 6,\n  name: \"3楼西区自习室\",\n  location: \"图书馆3楼西区\",\n  floor: 3,\n  capacity: 36,\n  availableSeats: 10,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆3楼西区自习室，靠近研讨室\"\n}, {\n  id: 7,\n  name: \"4楼东区自习室\",\n  location: \"图书馆4楼东区\",\n  floor: 4,\n  capacity: 36,\n  availableSeats: 28,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆4楼东区自习室，环境安静，靠近电梯\"\n}, {\n  id: 8,\n  name: \"4楼西区自习室\",\n  location: \"图书馆4楼西区\",\n  floor: 4,\n  capacity: 36,\n  availableSeats: 20,\n  openTime: \"08:00:00\",\n  closeTime: \"22:00:00\",\n  status: \"open\",\n  description: \"图书馆4楼西区自习室，靠近多媒体区\"\n}];\n\n// 生成座位数据\nexport const generateSeats = roomId => {\n  const seats = [];\n  for (let row = 1; row <= 6; row++) {\n    for (let col = 1; col <= 6; col++) {\n      // 靠边的座位：第1行、第6行、第1列、第6列\n      const isEdgeSeat = row === 1 || row === 6 || col === 1 || col === 6;\n\n      // 每隔一位有电源插座\n      const isPowerOutlet = isEdgeSeat && (row + col) % 2 === 0;\n\n      // 靠窗的座位：第1行和第6行\n      const isWindowSeat = row === 1 || row === 6;\n\n      // 随机生成座位状态\n      // eslint-disable-next-line no-unused-vars\n      const statusOptions = [\"available\", \"occupied\", \"disabled\"];\n      const randomIndex = Math.floor(Math.random() * 10);\n      let status;\n      if (randomIndex < 7) {\n        status = \"available\"; // 70%概率可用\n      } else if (randomIndex < 9) {\n        status = \"occupied\"; // 20%概率已占用\n      } else {\n        status = \"disabled\"; // 10%概率禁用\n      }\n      seats.push({\n        id: roomId * 100 + row * 10 + col,\n        roomId: roomId,\n        seatNumber: `${row}-${col}`,\n        row: row,\n        column: col,\n        status: status,\n        isPowerOutlet: isPowerOutlet,\n        isWindowSeat: isWindowSeat\n      });\n    }\n  }\n  return seats;\n};\n\n// 获取当前日期和时间\nconst today = new Date();\nconst tomorrow = new Date(today);\ntomorrow.setDate(today.getDate() + 1);\nconst dayAfterTomorrow = new Date(today);\ndayAfterTomorrow.setDate(today.getDate() + 2);\n\n// 格式化为ISO字符串\nconst todayStr = today.toISOString();\nconst tomorrowStr = tomorrow.toISOString();\nconst dayAfterTomorrowStr = dayAfterTomorrow.toISOString();\n\n// 我的预约\nexport const myReservations = [{\n  id: 1001,\n  seatId: 105,\n  roomId: 1,\n  roomName: \"1楼东区自习室\",\n  seatNumber: \"1-5\",\n  startTime: todayStr.split(\"T\")[0] + \"T09:00:00Z\",\n  endTime: todayStr.split(\"T\")[0] + \"T12:00:00Z\",\n  status: \"completed\",\n  checkInTime: todayStr.split(\"T\")[0] + \"T09:05:00Z\",\n  checkOutTime: todayStr.split(\"T\")[0] + \"T11:55:00Z\",\n  createdAt: todayStr\n}, {\n  id: 1002,\n  seatId: 304,\n  roomId: 3,\n  roomName: \"2楼东区自习室\",\n  seatNumber: \"3-4\",\n  startTime: tomorrowStr.split(\"T\")[0] + \"T14:00:00Z\",\n  endTime: tomorrowStr.split(\"T\")[0] + \"T18:00:00Z\",\n  status: \"pending\",\n  checkInTime: null,\n  checkOutTime: null,\n  createdAt: todayStr\n}, {\n  id: 1003,\n  seatId: 206,\n  roomId: 2,\n  roomName: \"1楼西区自习室\",\n  seatNumber: \"2-6\",\n  startTime: dayAfterTomorrowStr.split(\"T\")[0] + \"T09:00:00Z\",\n  endTime: dayAfterTomorrowStr.split(\"T\")[0] + \"T12:00:00Z\",\n  status: \"cancelled\",\n  checkInTime: null,\n  checkOutTime: null,\n  createdAt: todayStr\n}];\n\n// 操作记录\nexport const operationRecords = [{\n  id: 1,\n  action: \"reservation_create\",\n  targetType: \"seat\",\n  targetId: 105,\n  description: \"预约了1楼东区自习室的1-5座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: todayStr.split(\"T\")[0] + \"T08:30:00Z\"\n}, {\n  id: 2,\n  action: \"check_in\",\n  targetType: \"reservation\",\n  targetId: 1001,\n  description: \"签到了1楼东区自习室的1-5座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: todayStr.split(\"T\")[0] + \"T09:05:00Z\"\n}, {\n  id: 3,\n  action: \"check_out\",\n  targetType: \"reservation\",\n  targetId: 1001,\n  description: \"签退了1楼东区自习室的1-5座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: todayStr.split(\"T\")[0] + \"T11:55:00Z\"\n}, {\n  id: 4,\n  action: \"reservation_create\",\n  targetType: \"seat\",\n  targetId: 304,\n  description: \"预约了2楼东区自习室的3-4座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: todayStr.split(\"T\")[0] + \"T10:15:00Z\"\n}, {\n  id: 5,\n  action: \"reservation_create\",\n  targetType: \"seat\",\n  targetId: 206,\n  description: \"预约了1楼西区自习室的2-6座位\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: todayStr.split(\"T\")[0] + \"T16:45:00Z\"\n}, {\n  id: 6,\n  action: \"reservation_cancel\",\n  targetType: \"reservation\",\n  targetId: 1003,\n  description: \"取消了1楼西区自习室的2-6座位预约\",\n  status: \"success\",\n  ipAddress: \"*************\",\n  createdAt: todayStr.split(\"T\")[0] + \"T17:30:00Z\"\n}];\n\n// 信誉分记录\nexport const creditRecords = [{\n  id: 1,\n  score: -5,\n  reason: \"预约未签到\",\n  description: \"预约座位后未按时签到\",\n  createdAt: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString() // 30天前\n}, {\n  id: 2,\n  score: 5,\n  reason: \"按时签退\",\n  description: \"使用座位后按时签退\",\n  createdAt: new Date(today.getTime() - 20 * 24 * 60 * 60 * 1000).toISOString() // 20天前\n}, {\n  id: 3,\n  score: -10,\n  reason: \"违规占座\",\n  description: \"长时间离开座位未签退\",\n  createdAt: new Date(today.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString() // 10天前\n}, {\n  id: 4,\n  score: 5,\n  reason: \"按时签退\",\n  description: \"使用座位后按时签退\",\n  createdAt: todayStr.split(\"T\")[0] + \"T11:45:00Z\" // 今天\n}];\n\n// 时间段\nexport const timeSlots = [{\n  startTime: \"08:00:00\",\n  endTime: \"10:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"10:00:00\",\n  endTime: \"12:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"12:00:00\",\n  endTime: \"14:00:00\",\n  isAvailable: false\n}, {\n  startTime: \"14:00:00\",\n  endTime: \"16:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"16:00:00\",\n  endTime: \"18:00:00\",\n  isAvailable: true\n}, {\n  startTime: \"18:00:00\",\n  endTime: \"20:00:00\",\n  isAvailable: false\n}, {\n  startTime: \"20:00:00\",\n  endTime: \"22:00:00\",\n  isAvailable: true\n}];", "map": {"version": 3, "names": ["userInfo", "id", "studentIdHash", "email", "phone", "creditScore", "public<PERSON>ey", "privateKey", "lastLogin", "Date", "toISOString", "createdAt", "rooms", "name", "location", "floor", "capacity", "availableSeats", "openTime", "closeTime", "status", "description", "generateSeats", "roomId", "seats", "row", "col", "isEdgeSeat", "isPowerOutlet", "isWindowSeat", "statusOptions", "randomIndex", "Math", "random", "push", "seatNumber", "column", "today", "tomorrow", "setDate", "getDate", "dayAfterTomorrow", "todayStr", "tomorrowStr", "dayAfterTomorrowStr", "myReservations", "seatId", "roomName", "startTime", "split", "endTime", "checkInTime", "checkOutTime", "operationRecords", "action", "targetType", "targetId", "ip<PERSON><PERSON><PERSON>", "creditRecords", "score", "reason", "getTime", "timeSlots", "isAvailable"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/mock/data.js"], "sourcesContent": ["/**\n * 模拟数据\n * 用于前端开发阶段，提供静态数据以便展示界面效果\n */\n\n// 用户信息\nexport const userInfo = {\n  id: 1,\n  studentIdHash: \"20238273985\",\n  email: \"<EMAIL>\",\n  phone: \"18227789069\",\n  creditScore: 95,\n  publicKey:\n    \"04dd2902c59dcf271e904f33cfed28ad5a5d217bb8634b50afb704540af085f7d816a05a518477931613585fc405d2dde780364ffb937253167cf0614293ebfb63\",\n  privateKey:\n    \"95f766ab410295336506e9b784f714fcd20dc0df96c0c4d9ab7a3131e479c358\",\n  lastLogin: new Date().toISOString(),\n  createdAt: new Date().toISOString(),\n};\n\n// 自习室列表\nexport const rooms = [\n  {\n    id: 1,\n    name: \"1楼东区自习室\",\n    location: \"图书馆1楼东区\",\n    floor: 1,\n    capacity: 36,\n    availableSeats: 25,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆1楼东区自习室，环境安静，靠近入口\",\n  },\n  {\n    id: 2,\n    name: \"1楼西区自习室\",\n    location: \"图书馆1楼西区\",\n    floor: 1,\n    capacity: 36,\n    availableSeats: 18,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆1楼西区自习室，靠近图书借阅区\",\n  },\n  {\n    id: 3,\n    name: \"2楼东区自习室\",\n    location: \"图书馆2楼东区\",\n    floor: 2,\n    capacity: 36,\n    availableSeats: 30,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆2楼东区自习室，环境安静，靠近电梯\",\n  },\n  {\n    id: 4,\n    name: \"2楼西区自习室\",\n    location: \"图书馆2楼西区\",\n    floor: 2,\n    capacity: 36,\n    availableSeats: 22,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆2楼西区自习室，靠近期刊阅览区\",\n  },\n  {\n    id: 5,\n    name: \"3楼东区自习室\",\n    location: \"图书馆3楼东区\",\n    floor: 3,\n    capacity: 36,\n    availableSeats: 15,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆3楼东区自习室，环境安静，靠近电梯\",\n  },\n  {\n    id: 6,\n    name: \"3楼西区自习室\",\n    location: \"图书馆3楼西区\",\n    floor: 3,\n    capacity: 36,\n    availableSeats: 10,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆3楼西区自习室，靠近研讨室\",\n  },\n  {\n    id: 7,\n    name: \"4楼东区自习室\",\n    location: \"图书馆4楼东区\",\n    floor: 4,\n    capacity: 36,\n    availableSeats: 28,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆4楼东区自习室，环境安静，靠近电梯\",\n  },\n  {\n    id: 8,\n    name: \"4楼西区自习室\",\n    location: \"图书馆4楼西区\",\n    floor: 4,\n    capacity: 36,\n    availableSeats: 20,\n    openTime: \"08:00:00\",\n    closeTime: \"22:00:00\",\n    status: \"open\",\n    description: \"图书馆4楼西区自习室，靠近多媒体区\",\n  },\n];\n\n// 生成座位数据\nexport const generateSeats = (roomId) => {\n  const seats = [];\n  for (let row = 1; row <= 6; row++) {\n    for (let col = 1; col <= 6; col++) {\n      // 靠边的座位：第1行、第6行、第1列、第6列\n      const isEdgeSeat = row === 1 || row === 6 || col === 1 || col === 6;\n\n      // 每隔一位有电源插座\n      const isPowerOutlet = isEdgeSeat && (row + col) % 2 === 0;\n\n      // 靠窗的座位：第1行和第6行\n      const isWindowSeat = row === 1 || row === 6;\n\n      // 随机生成座位状态\n      // eslint-disable-next-line no-unused-vars\n      const statusOptions = [\"available\", \"occupied\", \"disabled\"];\n      const randomIndex = Math.floor(Math.random() * 10);\n      let status;\n      if (randomIndex < 7) {\n        status = \"available\"; // 70%概率可用\n      } else if (randomIndex < 9) {\n        status = \"occupied\"; // 20%概率已占用\n      } else {\n        status = \"disabled\"; // 10%概率禁用\n      }\n\n      seats.push({\n        id: roomId * 100 + row * 10 + col,\n        roomId: roomId,\n        seatNumber: `${row}-${col}`,\n        row: row,\n        column: col,\n        status: status,\n        isPowerOutlet: isPowerOutlet,\n        isWindowSeat: isWindowSeat,\n      });\n    }\n  }\n  return seats;\n};\n\n// 获取当前日期和时间\nconst today = new Date();\nconst tomorrow = new Date(today);\ntomorrow.setDate(today.getDate() + 1);\nconst dayAfterTomorrow = new Date(today);\ndayAfterTomorrow.setDate(today.getDate() + 2);\n\n// 格式化为ISO字符串\nconst todayStr = today.toISOString();\nconst tomorrowStr = tomorrow.toISOString();\nconst dayAfterTomorrowStr = dayAfterTomorrow.toISOString();\n\n// 我的预约\nexport const myReservations = [\n  {\n    id: 1001,\n    seatId: 105,\n    roomId: 1,\n    roomName: \"1楼东区自习室\",\n    seatNumber: \"1-5\",\n    startTime: todayStr.split(\"T\")[0] + \"T09:00:00Z\",\n    endTime: todayStr.split(\"T\")[0] + \"T12:00:00Z\",\n    status: \"completed\",\n    checkInTime: todayStr.split(\"T\")[0] + \"T09:05:00Z\",\n    checkOutTime: todayStr.split(\"T\")[0] + \"T11:55:00Z\",\n    createdAt: todayStr,\n  },\n  {\n    id: 1002,\n    seatId: 304,\n    roomId: 3,\n    roomName: \"2楼东区自习室\",\n    seatNumber: \"3-4\",\n    startTime: tomorrowStr.split(\"T\")[0] + \"T14:00:00Z\",\n    endTime: tomorrowStr.split(\"T\")[0] + \"T18:00:00Z\",\n    status: \"pending\",\n    checkInTime: null,\n    checkOutTime: null,\n    createdAt: todayStr,\n  },\n  {\n    id: 1003,\n    seatId: 206,\n    roomId: 2,\n    roomName: \"1楼西区自习室\",\n    seatNumber: \"2-6\",\n    startTime: dayAfterTomorrowStr.split(\"T\")[0] + \"T09:00:00Z\",\n    endTime: dayAfterTomorrowStr.split(\"T\")[0] + \"T12:00:00Z\",\n    status: \"cancelled\",\n    checkInTime: null,\n    checkOutTime: null,\n    createdAt: todayStr,\n  },\n];\n\n// 操作记录\nexport const operationRecords = [\n  {\n    id: 1,\n    action: \"reservation_create\",\n    targetType: \"seat\",\n    targetId: 105,\n    description: \"预约了1楼东区自习室的1-5座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: todayStr.split(\"T\")[0] + \"T08:30:00Z\",\n  },\n  {\n    id: 2,\n    action: \"check_in\",\n    targetType: \"reservation\",\n    targetId: 1001,\n    description: \"签到了1楼东区自习室的1-5座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: todayStr.split(\"T\")[0] + \"T09:05:00Z\",\n  },\n  {\n    id: 3,\n    action: \"check_out\",\n    targetType: \"reservation\",\n    targetId: 1001,\n    description: \"签退了1楼东区自习室的1-5座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: todayStr.split(\"T\")[0] + \"T11:55:00Z\",\n  },\n  {\n    id: 4,\n    action: \"reservation_create\",\n    targetType: \"seat\",\n    targetId: 304,\n    description: \"预约了2楼东区自习室的3-4座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: todayStr.split(\"T\")[0] + \"T10:15:00Z\",\n  },\n  {\n    id: 5,\n    action: \"reservation_create\",\n    targetType: \"seat\",\n    targetId: 206,\n    description: \"预约了1楼西区自习室的2-6座位\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: todayStr.split(\"T\")[0] + \"T16:45:00Z\",\n  },\n  {\n    id: 6,\n    action: \"reservation_cancel\",\n    targetType: \"reservation\",\n    targetId: 1003,\n    description: \"取消了1楼西区自习室的2-6座位预约\",\n    status: \"success\",\n    ipAddress: \"*************\",\n    createdAt: todayStr.split(\"T\")[0] + \"T17:30:00Z\",\n  },\n];\n\n// 信誉分记录\nexport const creditRecords = [\n  {\n    id: 1,\n    score: -5,\n    reason: \"预约未签到\",\n    description: \"预约座位后未按时签到\",\n    createdAt: new Date(\n      today.getTime() - 30 * 24 * 60 * 60 * 1000\n    ).toISOString(), // 30天前\n  },\n  {\n    id: 2,\n    score: 5,\n    reason: \"按时签退\",\n    description: \"使用座位后按时签退\",\n    createdAt: new Date(\n      today.getTime() - 20 * 24 * 60 * 60 * 1000\n    ).toISOString(), // 20天前\n  },\n  {\n    id: 3,\n    score: -10,\n    reason: \"违规占座\",\n    description: \"长时间离开座位未签退\",\n    createdAt: new Date(\n      today.getTime() - 10 * 24 * 60 * 60 * 1000\n    ).toISOString(), // 10天前\n  },\n  {\n    id: 4,\n    score: 5,\n    reason: \"按时签退\",\n    description: \"使用座位后按时签退\",\n    createdAt: todayStr.split(\"T\")[0] + \"T11:45:00Z\", // 今天\n  },\n];\n\n// 时间段\nexport const timeSlots = [\n  { startTime: \"08:00:00\", endTime: \"10:00:00\", isAvailable: true },\n  { startTime: \"10:00:00\", endTime: \"12:00:00\", isAvailable: true },\n  { startTime: \"12:00:00\", endTime: \"14:00:00\", isAvailable: false },\n  { startTime: \"14:00:00\", endTime: \"16:00:00\", isAvailable: true },\n  { startTime: \"16:00:00\", endTime: \"18:00:00\", isAvailable: true },\n  { startTime: \"18:00:00\", endTime: \"20:00:00\", isAvailable: false },\n  { startTime: \"20:00:00\", endTime: \"22:00:00\", isAvailable: true },\n];\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,QAAQ,GAAG;EACtBC,EAAE,EAAE,CAAC;EACLC,aAAa,EAAE,aAAa;EAC5BC,KAAK,EAAE,yBAAyB;EAChCC,KAAK,EAAE,aAAa;EACpBC,WAAW,EAAE,EAAE;EACfC,SAAS,EACP,oIAAoI;EACtIC,UAAU,EACR,kEAAkE;EACpEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;AACpC,CAAC;;AAED;AACA,OAAO,MAAME,KAAK,GAAG,CACnB;EACEX,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,EACD;EACEpB,EAAE,EAAE,CAAC;EACLY,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,aAAa,GAAIC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IACjC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;MACjC;MACA,MAAMC,UAAU,GAAGF,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;;MAEnE;MACA,MAAME,aAAa,GAAGD,UAAU,IAAI,CAACF,GAAG,GAAGC,GAAG,IAAI,CAAC,KAAK,CAAC;;MAEzD;MACA,MAAMG,YAAY,GAAGJ,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;;MAE3C;MACA;MACA,MAAMK,aAAa,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;MAC3D,MAAMC,WAAW,GAAGC,IAAI,CAACjB,KAAK,CAACiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;MAClD,IAAIb,MAAM;MACV,IAAIW,WAAW,GAAG,CAAC,EAAE;QACnBX,MAAM,GAAG,WAAW,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIW,WAAW,GAAG,CAAC,EAAE;QAC1BX,MAAM,GAAG,UAAU,CAAC,CAAC;MACvB,CAAC,MAAM;QACLA,MAAM,GAAG,UAAU,CAAC,CAAC;MACvB;MAEAI,KAAK,CAACU,IAAI,CAAC;QACTjC,EAAE,EAAEsB,MAAM,GAAG,GAAG,GAAGE,GAAG,GAAG,EAAE,GAAGC,GAAG;QACjCH,MAAM,EAAEA,MAAM;QACdY,UAAU,EAAE,GAAGV,GAAG,IAAIC,GAAG,EAAE;QAC3BD,GAAG,EAAEA,GAAG;QACRW,MAAM,EAAEV,GAAG;QACXN,MAAM,EAAEA,MAAM;QACdQ,aAAa,EAAEA,aAAa;QAC5BC,YAAY,EAAEA;MAChB,CAAC,CAAC;IACJ;EACF;EACA,OAAOL,KAAK;AACd,CAAC;;AAED;AACA,MAAMa,KAAK,GAAG,IAAI5B,IAAI,CAAC,CAAC;AACxB,MAAM6B,QAAQ,GAAG,IAAI7B,IAAI,CAAC4B,KAAK,CAAC;AAChCC,QAAQ,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACrC,MAAMC,gBAAgB,GAAG,IAAIhC,IAAI,CAAC4B,KAAK,CAAC;AACxCI,gBAAgB,CAACF,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;;AAE7C;AACA,MAAME,QAAQ,GAAGL,KAAK,CAAC3B,WAAW,CAAC,CAAC;AACpC,MAAMiC,WAAW,GAAGL,QAAQ,CAAC5B,WAAW,CAAC,CAAC;AAC1C,MAAMkC,mBAAmB,GAAGH,gBAAgB,CAAC/B,WAAW,CAAC,CAAC;;AAE1D;AACA,OAAO,MAAMmC,cAAc,GAAG,CAC5B;EACE5C,EAAE,EAAE,IAAI;EACR6C,MAAM,EAAE,GAAG;EACXvB,MAAM,EAAE,CAAC;EACTwB,QAAQ,EAAE,SAAS;EACnBZ,UAAU,EAAE,KAAK;EACjBa,SAAS,EAAEN,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EAChDC,OAAO,EAAER,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EAC9C7B,MAAM,EAAE,WAAW;EACnB+B,WAAW,EAAET,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EAClDG,YAAY,EAAEV,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EACnDtC,SAAS,EAAE+B;AACb,CAAC,EACD;EACEzC,EAAE,EAAE,IAAI;EACR6C,MAAM,EAAE,GAAG;EACXvB,MAAM,EAAE,CAAC;EACTwB,QAAQ,EAAE,SAAS;EACnBZ,UAAU,EAAE,KAAK;EACjBa,SAAS,EAAEL,WAAW,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EACnDC,OAAO,EAAEP,WAAW,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EACjD7B,MAAM,EAAE,SAAS;EACjB+B,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBzC,SAAS,EAAE+B;AACb,CAAC,EACD;EACEzC,EAAE,EAAE,IAAI;EACR6C,MAAM,EAAE,GAAG;EACXvB,MAAM,EAAE,CAAC;EACTwB,QAAQ,EAAE,SAAS;EACnBZ,UAAU,EAAE,KAAK;EACjBa,SAAS,EAAEJ,mBAAmB,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EAC3DC,OAAO,EAAEN,mBAAmB,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY;EACzD7B,MAAM,EAAE,WAAW;EACnB+B,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBzC,SAAS,EAAE+B;AACb,CAAC,CACF;;AAED;AACA,OAAO,MAAMW,gBAAgB,GAAG,CAC9B;EACEpD,EAAE,EAAE,CAAC;EACLqD,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,GAAG;EACbnC,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjBqC,SAAS,EAAE,eAAe;EAC1B9C,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AACtC,CAAC,EACD;EACEhD,EAAE,EAAE,CAAC;EACLqD,MAAM,EAAE,UAAU;EAClBC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,IAAI;EACdnC,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjBqC,SAAS,EAAE,eAAe;EAC1B9C,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AACtC,CAAC,EACD;EACEhD,EAAE,EAAE,CAAC;EACLqD,MAAM,EAAE,WAAW;EACnBC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,IAAI;EACdnC,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjBqC,SAAS,EAAE,eAAe;EAC1B9C,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AACtC,CAAC,EACD;EACEhD,EAAE,EAAE,CAAC;EACLqD,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,GAAG;EACbnC,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjBqC,SAAS,EAAE,eAAe;EAC1B9C,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AACtC,CAAC,EACD;EACEhD,EAAE,EAAE,CAAC;EACLqD,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,GAAG;EACbnC,WAAW,EAAE,kBAAkB;EAC/BD,MAAM,EAAE,SAAS;EACjBqC,SAAS,EAAE,eAAe;EAC1B9C,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AACtC,CAAC,EACD;EACEhD,EAAE,EAAE,CAAC;EACLqD,MAAM,EAAE,oBAAoB;EAC5BC,UAAU,EAAE,aAAa;EACzBC,QAAQ,EAAE,IAAI;EACdnC,WAAW,EAAE,oBAAoB;EACjCD,MAAM,EAAE,SAAS;EACjBqC,SAAS,EAAE,eAAe;EAC1B9C,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;AACtC,CAAC,CACF;;AAED;AACA,OAAO,MAAMS,aAAa,GAAG,CAC3B;EACEzD,EAAE,EAAE,CAAC;EACL0D,KAAK,EAAE,CAAC,CAAC;EACTC,MAAM,EAAE,OAAO;EACfvC,WAAW,EAAE,YAAY;EACzBV,SAAS,EAAE,IAAIF,IAAI,CACjB4B,KAAK,CAACwB,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IACxC,CAAC,CAACnD,WAAW,CAAC,CAAC,CAAE;AACnB,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACL0D,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,MAAM;EACdvC,WAAW,EAAE,WAAW;EACxBV,SAAS,EAAE,IAAIF,IAAI,CACjB4B,KAAK,CAACwB,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IACxC,CAAC,CAACnD,WAAW,CAAC,CAAC,CAAE;AACnB,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACL0D,KAAK,EAAE,CAAC,EAAE;EACVC,MAAM,EAAE,MAAM;EACdvC,WAAW,EAAE,YAAY;EACzBV,SAAS,EAAE,IAAIF,IAAI,CACjB4B,KAAK,CAACwB,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IACxC,CAAC,CAACnD,WAAW,CAAC,CAAC,CAAE;AACnB,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACL0D,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,MAAM;EACdvC,WAAW,EAAE,WAAW;EACxBV,SAAS,EAAE+B,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAE;AACpD,CAAC,CACF;;AAED;AACA,OAAO,MAAMa,SAAS,GAAG,CACvB;EAAEd,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEf,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEf,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAM,CAAC,EAClE;EAAEf,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEf,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAK,CAAC,EACjE;EAAEf,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAM,CAAC,EAClE;EAAEf,SAAS,EAAE,UAAU;EAAEE,OAAO,EAAE,UAAU;EAAEa,WAAW,EAAE;AAAK,CAAC,CAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}