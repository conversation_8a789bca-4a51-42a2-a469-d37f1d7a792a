{"ast": null, "code": "import http from \"./http\";\nexport default {\n  // 获取自习室列表\n  getRooms() {\n    return http.get(\"/seat/rooms\");\n  },\n  // 获取自习室详情\n  getRoomById(roomId) {\n    return http.get(`/seat/rooms/${roomId}`);\n  },\n  // 获取自习室座位列表\n  getSeatsByRoom(roomId, date) {\n    return http.get(`/seat/rooms/${roomId}/seats`, {\n      params: {\n        date\n      }\n    });\n  },\n  // 获取座位详情\n  getSeatById(seatId) {\n    return http.get(`/seat/seats/${seatId}`);\n  },\n  // 获取座位可用时间段\n  getSeatTimeSlots(seatId, date) {\n    return http.get(`/seat/seats/${seatId}/time-slots`, {\n      params: {\n        date\n      }\n    });\n  },\n  // 创建座位预约\n  createReservation(seatId, startTime, endTime) {\n    return http.post(\"/seat/reservations\", {\n      seatId,\n      startTime,\n      endTime\n    });\n  },\n  // 获取我的预约列表\n  getMyReservations() {\n    return http.get(\"/seat/reservations/my\");\n  },\n  // 获取预约详情\n  getReservationById(reservationId) {\n    return http.get(`/seat/reservations/${reservationId}`);\n  },\n  // 获取活跃预约\n  getActiveReservation() {\n    return http.get(\"/seat/reservations/active\");\n  },\n  // 签到\n  checkIn(requestData) {\n    return http.post(\"/seat/reservations/check_in\", requestData);\n  },\n  // 签退\n  checkOut(requestData) {\n    return http.post(\"/seat/reservations/check_out\", requestData);\n  },\n  // 取消预约\n  cancelReservation(reservationId) {\n    return http.post(`/seat/reservations/${reservationId}/cancel`);\n  },\n  // 获取操作记录\n  getOperationRecords(params) {\n    return http.get(\"/seat/operations\", {\n      params\n    });\n  },\n  // 获取预约历史记录\n  getReservationHistory(params) {\n    return http.get(\"/seat/reservations/history\", {\n      params\n    });\n  }\n};", "map": {"version": 3, "names": ["http", "getRooms", "get", "getRoomById", "roomId", "getSeatsByRoom", "date", "params", "getSeatById", "seatId", "getSeatTimeSlots", "createReservation", "startTime", "endTime", "post", "getMyReservations", "getReservationById", "reservationId", "getActiveReservation", "checkIn", "requestData", "checkOut", "cancelReservation", "getOperationRecords", "getReservationHistory"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/api/seat.js"], "sourcesContent": ["import http from \"./http\";\n\nexport default {\n  // 获取自习室列表\n  getRooms() {\n    return http.get(\"/seat/rooms\");\n  },\n\n  // 获取自习室详情\n  getRoomById(roomId) {\n    return http.get(`/seat/rooms/${roomId}`);\n  },\n\n  // 获取自习室座位列表\n  getSeatsByRoom(roomId, date) {\n    return http.get(`/seat/rooms/${roomId}/seats`, { params: { date } });\n  },\n\n  // 获取座位详情\n  getSeatById(seatId) {\n    return http.get(`/seat/seats/${seatId}`);\n  },\n\n  // 获取座位可用时间段\n  getSeatTimeSlots(seatId, date) {\n    return http.get(`/seat/seats/${seatId}/time-slots`, { params: { date } });\n  },\n\n  // 创建座位预约\n  createReservation(seatId, startTime, endTime) {\n    return http.post(\"/seat/reservations\", { seatId, startTime, endTime });\n  },\n\n  // 获取我的预约列表\n  getMyReservations() {\n    return http.get(\"/seat/reservations/my\");\n  },\n\n  // 获取预约详情\n  getReservationById(reservationId) {\n    return http.get(`/seat/reservations/${reservationId}`);\n  },\n\n  // 获取活跃预约\n  getActiveReservation() {\n    return http.get(\"/seat/reservations/active\");\n  },\n\n  // 签到\n  checkIn(requestData) {\n    return http.post(\"/seat/reservations/check_in\", requestData);\n  },\n\n  // 签退\n  checkOut(requestData) {\n    return http.post(\"/seat/reservations/check_out\", requestData);\n  },\n\n  // 取消预约\n  cancelReservation(reservationId) {\n    return http.post(`/seat/reservations/${reservationId}/cancel`);\n  },\n\n  // 获取操作记录\n  getOperationRecords(params) {\n    return http.get(\"/seat/operations\", { params });\n  },\n\n  // 获取预约历史记录\n  getReservationHistory(params) {\n    return http.get(\"/seat/reservations/history\", { params });\n  },\n};\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AAEzB,eAAe;EACb;EACAC,QAAQA,CAAA,EAAG;IACT,OAAOD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAC;EAChC,CAAC;EAED;EACAC,WAAWA,CAACC,MAAM,EAAE;IAClB,OAAOJ,IAAI,CAACE,GAAG,CAAC,eAAeE,MAAM,EAAE,CAAC;EAC1C,CAAC;EAED;EACAC,cAAcA,CAACD,MAAM,EAAEE,IAAI,EAAE;IAC3B,OAAON,IAAI,CAACE,GAAG,CAAC,eAAeE,MAAM,QAAQ,EAAE;MAAEG,MAAM,EAAE;QAAED;MAAK;IAAE,CAAC,CAAC;EACtE,CAAC;EAED;EACAE,WAAWA,CAACC,MAAM,EAAE;IAClB,OAAOT,IAAI,CAACE,GAAG,CAAC,eAAeO,MAAM,EAAE,CAAC;EAC1C,CAAC;EAED;EACAC,gBAAgBA,CAACD,MAAM,EAAEH,IAAI,EAAE;IAC7B,OAAON,IAAI,CAACE,GAAG,CAAC,eAAeO,MAAM,aAAa,EAAE;MAAEF,MAAM,EAAE;QAAED;MAAK;IAAE,CAAC,CAAC;EAC3E,CAAC;EAED;EACAK,iBAAiBA,CAACF,MAAM,EAAEG,SAAS,EAAEC,OAAO,EAAE;IAC5C,OAAOb,IAAI,CAACc,IAAI,CAAC,oBAAoB,EAAE;MAAEL,MAAM;MAAEG,SAAS;MAAEC;IAAQ,CAAC,CAAC;EACxE,CAAC;EAED;EACAE,iBAAiBA,CAAA,EAAG;IAClB,OAAOf,IAAI,CAACE,GAAG,CAAC,uBAAuB,CAAC;EAC1C,CAAC;EAED;EACAc,kBAAkBA,CAACC,aAAa,EAAE;IAChC,OAAOjB,IAAI,CAACE,GAAG,CAAC,sBAAsBe,aAAa,EAAE,CAAC;EACxD,CAAC;EAED;EACAC,oBAAoBA,CAAA,EAAG;IACrB,OAAOlB,IAAI,CAACE,GAAG,CAAC,2BAA2B,CAAC;EAC9C,CAAC;EAED;EACAiB,OAAOA,CAACC,WAAW,EAAE;IACnB,OAAOpB,IAAI,CAACc,IAAI,CAAC,6BAA6B,EAAEM,WAAW,CAAC;EAC9D,CAAC;EAED;EACAC,QAAQA,CAACD,WAAW,EAAE;IACpB,OAAOpB,IAAI,CAACc,IAAI,CAAC,8BAA8B,EAAEM,WAAW,CAAC;EAC/D,CAAC;EAED;EACAE,iBAAiBA,CAACL,aAAa,EAAE;IAC/B,OAAOjB,IAAI,CAACc,IAAI,CAAC,sBAAsBG,aAAa,SAAS,CAAC;EAChE,CAAC;EAED;EACAM,mBAAmBA,CAAChB,MAAM,EAAE;IAC1B,OAAOP,IAAI,CAACE,GAAG,CAAC,kBAAkB,EAAE;MAAEK;IAAO,CAAC,CAAC;EACjD,CAAC;EAED;EACAiB,qBAAqBA,CAACjB,MAAM,EAAE;IAC5B,OAAOP,IAAI,CAACE,GAAG,CAAC,4BAA4B,EAAE;MAAEK;IAAO,CAAC,CAAC;EAC3D;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}