/**
 * 模拟数据
 * 用于前端开发阶段，提供静态数据以便展示界面效果
 */

// 用户信息
export const userInfo = {
  id: 1,
  studentIdHash: "20238273985",
  email: "<EMAIL>",
  phone: "18227789069",
  creditScore: 95,
  publicKey:
    "04dd2902c59dcf271e904f33cfed28ad5a5d217bb8634b50afb704540af085f7d816a05a518477931613585fc405d2dde780364ffb937253167cf0614293ebfb63",
  privateKey:
    "95f766ab410295336506e9b784f714fcd20dc0df96c0c4d9ab7a3131e479c358",
  lastLogin: new Date().toISOString(),
  createdAt: new Date().toISOString(),
};

// 自习室列表
export const rooms = [
  {
    id: 1,
    name: "1楼东区自习室",
    location: "图书馆1楼东区",
    floor: 1,
    capacity: 36,
    availableSeats: 25,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆1楼东区自习室，环境安静，靠近入口",
  },
  {
    id: 2,
    name: "1楼西区自习室",
    location: "图书馆1楼西区",
    floor: 1,
    capacity: 36,
    availableSeats: 18,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆1楼西区自习室，靠近图书借阅区",
  },
  {
    id: 3,
    name: "2楼东区自习室",
    location: "图书馆2楼东区",
    floor: 2,
    capacity: 36,
    availableSeats: 30,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆2楼东区自习室，环境安静，靠近电梯",
  },
  {
    id: 4,
    name: "2楼西区自习室",
    location: "图书馆2楼西区",
    floor: 2,
    capacity: 36,
    availableSeats: 22,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆2楼西区自习室，靠近期刊阅览区",
  },
  {
    id: 5,
    name: "3楼东区自习室",
    location: "图书馆3楼东区",
    floor: 3,
    capacity: 36,
    availableSeats: 15,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆3楼东区自习室，环境安静，靠近电梯",
  },
  {
    id: 6,
    name: "3楼西区自习室",
    location: "图书馆3楼西区",
    floor: 3,
    capacity: 36,
    availableSeats: 10,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆3楼西区自习室，靠近研讨室",
  },
  {
    id: 7,
    name: "4楼东区自习室",
    location: "图书馆4楼东区",
    floor: 4,
    capacity: 36,
    availableSeats: 28,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆4楼东区自习室，环境安静，靠近电梯",
  },
  {
    id: 8,
    name: "4楼西区自习室",
    location: "图书馆4楼西区",
    floor: 4,
    capacity: 36,
    availableSeats: 20,
    openTime: "08:00:00",
    closeTime: "22:00:00",
    status: "open",
    description: "图书馆4楼西区自习室，靠近多媒体区",
  },
];

// 生成座位数据
export const generateSeats = (roomId) => {
  const seats = [];
  for (let row = 1; row <= 6; row++) {
    for (let col = 1; col <= 6; col++) {
      // 靠边的座位：第1行、第6行、第1列、第6列
      const isEdgeSeat = row === 1 || row === 6 || col === 1 || col === 6;

      // 每隔一位有电源插座
      const isPowerOutlet = isEdgeSeat && (row + col) % 2 === 0;

      // 靠窗的座位：第1行和第6行
      const isWindowSeat = row === 1 || row === 6;

      // 随机生成座位状态
      // eslint-disable-next-line no-unused-vars
      const statusOptions = ["available", "occupied", "disabled"];
      const randomIndex = Math.floor(Math.random() * 10);
      let status;
      if (randomIndex < 7) {
        status = "available"; // 70%概率可用
      } else if (randomIndex < 9) {
        status = "occupied"; // 20%概率已占用
      } else {
        status = "disabled"; // 10%概率禁用
      }

      seats.push({
        id: roomId * 100 + row * 10 + col,
        roomId: roomId,
        seatNumber: `${row}-${col}`,
        row: row,
        column: col,
        status: status,
        isPowerOutlet: isPowerOutlet,
        isWindowSeat: isWindowSeat,
      });
    }
  }
  return seats;
};

// 获取当前日期和时间
const today = new Date();
const tomorrow = new Date(today);
tomorrow.setDate(today.getDate() + 1);
const dayAfterTomorrow = new Date(today);
dayAfterTomorrow.setDate(today.getDate() + 2);

// 格式化为ISO字符串
const todayStr = today.toISOString();
const tomorrowStr = tomorrow.toISOString();
const dayAfterTomorrowStr = dayAfterTomorrow.toISOString();

// 我的预约
export const myReservations = [
  {
    id: 1001,
    seatId: 105,
    roomId: 1,
    roomName: "1楼东区自习室",
    seatNumber: "1-5",
    startTime: todayStr.split("T")[0] + "T09:00:00Z",
    endTime: todayStr.split("T")[0] + "T12:00:00Z",
    status: "completed",
    checkInTime: todayStr.split("T")[0] + "T09:05:00Z",
    checkOutTime: todayStr.split("T")[0] + "T11:55:00Z",
    createdAt: todayStr,
  },
  {
    id: 1002,
    seatId: 304,
    roomId: 3,
    roomName: "2楼东区自习室",
    seatNumber: "3-4",
    startTime: tomorrowStr.split("T")[0] + "T14:00:00Z",
    endTime: tomorrowStr.split("T")[0] + "T18:00:00Z",
    status: "pending",
    checkInTime: null,
    checkOutTime: null,
    createdAt: todayStr,
  },
  {
    id: 1003,
    seatId: 206,
    roomId: 2,
    roomName: "1楼西区自习室",
    seatNumber: "2-6",
    startTime: dayAfterTomorrowStr.split("T")[0] + "T09:00:00Z",
    endTime: dayAfterTomorrowStr.split("T")[0] + "T12:00:00Z",
    status: "cancelled",
    checkInTime: null,
    checkOutTime: null,
    createdAt: todayStr,
  },
];

// 操作记录
export const operationRecords = [
  {
    id: 1,
    action: "reservation_create",
    targetType: "seat",
    targetId: 105,
    description: "预约了1楼东区自习室的1-5座位",
    status: "success",
    ipAddress: "*************",
    createdAt: todayStr.split("T")[0] + "T08:30:00Z",
  },
  {
    id: 2,
    action: "check_in",
    targetType: "reservation",
    targetId: 1001,
    description: "签到了1楼东区自习室的1-5座位",
    status: "success",
    ipAddress: "*************",
    createdAt: todayStr.split("T")[0] + "T09:05:00Z",
  },
  {
    id: 3,
    action: "check_out",
    targetType: "reservation",
    targetId: 1001,
    description: "签退了1楼东区自习室的1-5座位",
    status: "success",
    ipAddress: "*************",
    createdAt: todayStr.split("T")[0] + "T11:55:00Z",
  },
  {
    id: 4,
    action: "reservation_create",
    targetType: "seat",
    targetId: 304,
    description: "预约了2楼东区自习室的3-4座位",
    status: "success",
    ipAddress: "*************",
    createdAt: todayStr.split("T")[0] + "T10:15:00Z",
  },
  {
    id: 5,
    action: "reservation_create",
    targetType: "seat",
    targetId: 206,
    description: "预约了1楼西区自习室的2-6座位",
    status: "success",
    ipAddress: "*************",
    createdAt: todayStr.split("T")[0] + "T16:45:00Z",
  },
  {
    id: 6,
    action: "reservation_cancel",
    targetType: "reservation",
    targetId: 1003,
    description: "取消了1楼西区自习室的2-6座位预约",
    status: "success",
    ipAddress: "*************",
    createdAt: todayStr.split("T")[0] + "T17:30:00Z",
  },
];

// 信誉分记录
export const creditRecords = [
  {
    id: 1,
    score: -5,
    reason: "预约未签到",
    description: "预约座位后未按时签到",
    createdAt: new Date(
      today.getTime() - 30 * 24 * 60 * 60 * 1000
    ).toISOString(), // 30天前
  },
  {
    id: 2,
    score: 5,
    reason: "按时签退",
    description: "使用座位后按时签退",
    createdAt: new Date(
      today.getTime() - 20 * 24 * 60 * 60 * 1000
    ).toISOString(), // 20天前
  },
  {
    id: 3,
    score: -10,
    reason: "违规占座",
    description: "长时间离开座位未签退",
    createdAt: new Date(
      today.getTime() - 10 * 24 * 60 * 60 * 1000
    ).toISOString(), // 10天前
  },
  {
    id: 4,
    score: 5,
    reason: "按时签退",
    description: "使用座位后按时签退",
    createdAt: todayStr.split("T")[0] + "T11:45:00Z", // 今天
  },
];

// 时间段
export const timeSlots = [
  { startTime: "08:00:00", endTime: "10:00:00", isAvailable: true },
  { startTime: "10:00:00", endTime: "12:00:00", isAvailable: true },
  { startTime: "12:00:00", endTime: "14:00:00", isAvailable: false },
  { startTime: "14:00:00", endTime: "16:00:00", isAvailable: true },
  { startTime: "16:00:00", endTime: "18:00:00", isAvailable: true },
  { startTime: "18:00:00", endTime: "20:00:00", isAvailable: false },
  { startTime: "20:00:00", endTime: "22:00:00", isAvailable: true },
];
