{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/**\n * WebSocket连接管理工具\n */\nimport { ElMessage } from \"element-plus\";\nimport store from \"@/store\";\nclass WebSocketManager {\n  constructor() {\n    this.connections = new Map();\n    this.reconnectAttempts = new Map();\n    this.maxReconnectAttempts = 5;\n    this.reconnectInterval = 3000;\n  }\n\n  /**\n   * 创建WebSocket连接\n   * @param {string} url - WebSocket URL\n   * @param {string} name - 连接名称\n   * @param {Object} options - 选项\n   * @returns {WebSocket}\n   */\n  connect(url, name, options = {}) {\n    if (this.connections.has(name)) {\n      this.disconnect(name);\n    }\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      console.error(\"WebSocket连接失败: 缺少认证令牌\");\n      return null;\n    }\n    const ws = new WebSocket(url);\n    const connectionInfo = {\n      ws,\n      url,\n      options,\n      isAuthenticated: false,\n      messageQueue: []\n    };\n    this.connections.set(name, connectionInfo);\n    this.reconnectAttempts.set(name, 0);\n\n    // 连接打开\n    ws.onopen = () => {\n      console.log(`WebSocket连接已建立: ${name}`);\n\n      // 发送认证消息\n      this.authenticate(name, token);\n      if (options.onOpen) {\n        options.onOpen();\n      }\n    };\n\n    // 接收消息\n    ws.onmessage = event => {\n      try {\n        const data = JSON.parse(event.data);\n        this.handleMessage(name, data);\n      } catch (error) {\n        console.error(\"WebSocket消息解析失败:\", error);\n      }\n    };\n\n    // 连接关闭\n    ws.onclose = event => {\n      console.log(`WebSocket连接已关闭: ${name}`, event.code, event.reason);\n      if (options.onClose) {\n        options.onClose(event);\n      }\n\n      // 自动重连\n      if (event.code !== 1000 && this.shouldReconnect(name)) {\n        this.scheduleReconnect(name);\n      }\n    };\n\n    // 连接错误\n    ws.onerror = error => {\n      console.error(`WebSocket连接错误: ${name}`, error);\n      if (options.onError) {\n        options.onError(error);\n      }\n    };\n    return ws;\n  }\n\n  /**\n   * 认证WebSocket连接\n   * @param {string} name - 连接名称\n   * @param {string} token - JWT令牌\n   */\n  authenticate(name, token) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n    const authMessage = {\n      type: \"authenticate\",\n      token: token\n    };\n    this.send(name, authMessage);\n  }\n\n  /**\n   * 发送消息\n   * @param {string} name - 连接名称\n   * @param {Object} message - 消息对象\n   */\n  send(name, message) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) {\n      console.error(`WebSocket连接不存在: ${name}`);\n      return false;\n    }\n    const {\n      ws,\n      isAuthenticated,\n      messageQueue\n    } = connectionInfo;\n    if (ws.readyState === WebSocket.OPEN) {\n      if (message.type === \"authenticate\" || isAuthenticated) {\n        ws.send(JSON.stringify(message));\n        return true;\n      } else {\n        // 如果未认证，将消息加入队列\n        messageQueue.push(message);\n        return false;\n      }\n    } else {\n      console.error(`WebSocket连接未就绪: ${name}`);\n      return false;\n    }\n  }\n\n  /**\n   * 处理接收到的消息\n   * @param {string} name - 连接名称\n   * @param {Object} data - 消息数据\n   */\n  handleMessage(name, data) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n    const {\n      options,\n      messageQueue\n    } = connectionInfo;\n    switch (data.type) {\n      case \"connection_established\":\n        console.log(`WebSocket连接建立确认: ${name}`);\n        break;\n      case \"auth_success\":\n        console.log(`WebSocket认证成功: ${name}`);\n        connectionInfo.isAuthenticated = true;\n\n        // 发送队列中的消息\n        while (messageQueue.length > 0) {\n          const queuedMessage = messageQueue.shift();\n          this.send(name, queuedMessage);\n        }\n        if (options.onAuthenticated) {\n          options.onAuthenticated(data);\n        }\n        break;\n      case \"auth_error\":\n        console.error(`WebSocket认证失败: ${name}`, data.message);\n        ElMessage.error(`WebSocket认证失败: ${data.message}`);\n        break;\n      case \"seat_status_update\":\n        this.handleSeatStatusUpdate(data.data);\n        break;\n      case \"reservation_update\":\n        this.handleReservationUpdate(data.data);\n        break;\n      case \"reservation_reminder\":\n        this.handleReservationReminder(data.data);\n        break;\n      case \"system_notification\":\n        this.handleSystemNotification(data.data);\n        break;\n      case \"error\":\n        console.error(`WebSocket错误: ${name}`, data.message);\n        ElMessage.error(data.message);\n        break;\n      default:\n        if (options.onMessage) {\n          options.onMessage(data);\n        }\n        break;\n    }\n  }\n\n  /**\n   * 处理座位状态更新\n   * @param {Object} seatData - 座位数据\n   */\n  handleSeatStatusUpdate(seatData) {\n    // 更新Vuex中的座位状态\n    store.dispatch(\"seat/updateSeatStatus\", seatData);\n  }\n\n  /**\n   * 处理预约更新\n   * @param {Object} reservationData - 预约数据\n   */\n  handleReservationUpdate(reservationData) {\n    // 更新Vuex中的预约状态\n    store.dispatch(\"seat/updateReservationStatus\", reservationData);\n  }\n\n  /**\n   * 处理预约提醒\n   * @param {Object} reminderData - 提醒数据\n   */\n  handleReservationReminder(reminderData) {\n    ElMessage({\n      title: \"预约提醒\",\n      message: `您的预约即将开始，请及时签到`,\n      type: \"warning\",\n      duration: 10000\n    });\n  }\n\n  /**\n   * 处理系统通知\n   * @param {Object} notificationData - 通知数据\n   */\n  handleSystemNotification(notificationData) {\n    ElMessage({\n      title: notificationData.title,\n      message: notificationData.message,\n      type: notificationData.type || \"info\",\n      duration: 5000\n    });\n  }\n\n  /**\n   * 订阅座位状态\n   * @param {string} name - 连接名称\n   * @param {number} roomId - 房间ID\n   */\n  subscribeSeatStatus(name, roomId) {\n    const message = {\n      type: \"subscribe\",\n      channel: \"seat_status\",\n      room_id: roomId\n    };\n    this.send(name, message);\n  }\n\n  /**\n   * 取消订阅座位状态\n   * @param {string} name - 连接名称\n   * @param {number} roomId - 房间ID\n   */\n  unsubscribeSeatStatus(name, roomId) {\n    const message = {\n      type: \"unsubscribe\",\n      channel: \"seat_status\",\n      room_id: roomId\n    };\n    this.send(name, message);\n  }\n\n  /**\n   * 断开WebSocket连接\n   * @param {string} name - 连接名称\n   */\n  disconnect(name) {\n    const connectionInfo = this.connections.get(name);\n    if (connectionInfo) {\n      connectionInfo.ws.close(1000, \"主动断开连接\");\n      this.connections.delete(name);\n      this.reconnectAttempts.delete(name);\n    }\n  }\n\n  /**\n   * 断开所有WebSocket连接\n   */\n  disconnectAll() {\n    for (const name of this.connections.keys()) {\n      this.disconnect(name);\n    }\n  }\n\n  /**\n   * 检查是否应该重连\n   * @param {string} name - 连接名称\n   * @returns {boolean}\n   */\n  shouldReconnect(name) {\n    const attempts = this.reconnectAttempts.get(name) || 0;\n    return attempts < this.maxReconnectAttempts;\n  }\n\n  /**\n   * 安排重连\n   * @param {string} name - 连接名称\n   */\n  scheduleReconnect(name) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n    const attempts = this.reconnectAttempts.get(name) || 0;\n    this.reconnectAttempts.set(name, attempts + 1);\n    console.log(`安排WebSocket重连: ${name}, 尝试次数: ${attempts + 1}`);\n    setTimeout(() => {\n      if (this.connections.has(name)) {\n        this.connect(connectionInfo.url, name, connectionInfo.options);\n      }\n    }, this.reconnectInterval * (attempts + 1));\n  }\n\n  /**\n   * 获取连接状态\n   * @param {string} name - 连接名称\n   * @returns {number|null}\n   */\n  getConnectionState(name) {\n    const connectionInfo = this.connections.get(name);\n    return connectionInfo ? connectionInfo.ws.readyState : null;\n  }\n\n  /**\n   * 检查连接是否已认证\n   * @param {string} name - 连接名称\n   * @returns {boolean}\n   */\n  isAuthenticated(name) {\n    const connectionInfo = this.connections.get(name);\n    return connectionInfo ? connectionInfo.isAuthenticated : false;\n  }\n}\n\n// 创建全局WebSocket管理器实例\nconst wsManager = new WebSocketManager();\nexport default wsManager;", "map": {"version": 3, "names": ["ElMessage", "store", "WebSocketManager", "constructor", "connections", "Map", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "connect", "url", "name", "options", "has", "disconnect", "token", "localStorage", "getItem", "console", "error", "ws", "WebSocket", "connectionInfo", "isAuthenticated", "messageQueue", "set", "onopen", "log", "authenticate", "onOpen", "onmessage", "event", "data", "JSON", "parse", "handleMessage", "onclose", "code", "reason", "onClose", "shouldReconnect", "scheduleReconnect", "onerror", "onError", "get", "authMessage", "type", "send", "message", "readyState", "OPEN", "stringify", "push", "length", "queuedMessage", "shift", "onAuthenticated", "handleSeatStatusUpdate", "handleReservationUpdate", "handleReservationReminder", "handleSystemNotification", "onMessage", "seatData", "dispatch", "reservationData", "reminderData", "title", "duration", "notificationData", "subscribeSeatStatus", "roomId", "channel", "room_id", "unsubscribeSeatStatus", "close", "delete", "disconnectAll", "keys", "attempts", "setTimeout", "getConnectionState", "wsManager"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/utils/websocket.js"], "sourcesContent": ["/**\n * WebSocket连接管理工具\n */\nimport { ElMessage } from \"element-plus\";\nimport store from \"@/store\";\n\nclass WebSocketManager {\n  constructor() {\n    this.connections = new Map();\n    this.reconnectAttempts = new Map();\n    this.maxReconnectAttempts = 5;\n    this.reconnectInterval = 3000;\n  }\n\n  /**\n   * 创建WebSocket连接\n   * @param {string} url - WebSocket URL\n   * @param {string} name - 连接名称\n   * @param {Object} options - 选项\n   * @returns {WebSocket}\n   */\n  connect(url, name, options = {}) {\n    if (this.connections.has(name)) {\n      this.disconnect(name);\n    }\n\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      console.error(\"WebSocket连接失败: 缺少认证令牌\");\n      return null;\n    }\n\n    const ws = new WebSocket(url);\n    const connectionInfo = {\n      ws,\n      url,\n      options,\n      isAuthenticated: false,\n      messageQueue: [],\n    };\n\n    this.connections.set(name, connectionInfo);\n    this.reconnectAttempts.set(name, 0);\n\n    // 连接打开\n    ws.onopen = () => {\n      console.log(`WebSocket连接已建立: ${name}`);\n      \n      // 发送认证消息\n      this.authenticate(name, token);\n      \n      if (options.onOpen) {\n        options.onOpen();\n      }\n    };\n\n    // 接收消息\n    ws.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        this.handleMessage(name, data);\n      } catch (error) {\n        console.error(\"WebSocket消息解析失败:\", error);\n      }\n    };\n\n    // 连接关闭\n    ws.onclose = (event) => {\n      console.log(`WebSocket连接已关闭: ${name}`, event.code, event.reason);\n      \n      if (options.onClose) {\n        options.onClose(event);\n      }\n\n      // 自动重连\n      if (event.code !== 1000 && this.shouldReconnect(name)) {\n        this.scheduleReconnect(name);\n      }\n    };\n\n    // 连接错误\n    ws.onerror = (error) => {\n      console.error(`WebSocket连接错误: ${name}`, error);\n      \n      if (options.onError) {\n        options.onError(error);\n      }\n    };\n\n    return ws;\n  }\n\n  /**\n   * 认证WebSocket连接\n   * @param {string} name - 连接名称\n   * @param {string} token - JWT令牌\n   */\n  authenticate(name, token) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n\n    const authMessage = {\n      type: \"authenticate\",\n      token: token,\n    };\n\n    this.send(name, authMessage);\n  }\n\n  /**\n   * 发送消息\n   * @param {string} name - 连接名称\n   * @param {Object} message - 消息对象\n   */\n  send(name, message) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) {\n      console.error(`WebSocket连接不存在: ${name}`);\n      return false;\n    }\n\n    const { ws, isAuthenticated, messageQueue } = connectionInfo;\n\n    if (ws.readyState === WebSocket.OPEN) {\n      if (message.type === \"authenticate\" || isAuthenticated) {\n        ws.send(JSON.stringify(message));\n        return true;\n      } else {\n        // 如果未认证，将消息加入队列\n        messageQueue.push(message);\n        return false;\n      }\n    } else {\n      console.error(`WebSocket连接未就绪: ${name}`);\n      return false;\n    }\n  }\n\n  /**\n   * 处理接收到的消息\n   * @param {string} name - 连接名称\n   * @param {Object} data - 消息数据\n   */\n  handleMessage(name, data) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n\n    const { options, messageQueue } = connectionInfo;\n\n    switch (data.type) {\n      case \"connection_established\":\n        console.log(`WebSocket连接建立确认: ${name}`);\n        break;\n\n      case \"auth_success\":\n        console.log(`WebSocket认证成功: ${name}`);\n        connectionInfo.isAuthenticated = true;\n        \n        // 发送队列中的消息\n        while (messageQueue.length > 0) {\n          const queuedMessage = messageQueue.shift();\n          this.send(name, queuedMessage);\n        }\n        \n        if (options.onAuthenticated) {\n          options.onAuthenticated(data);\n        }\n        break;\n\n      case \"auth_error\":\n        console.error(`WebSocket认证失败: ${name}`, data.message);\n        ElMessage.error(`WebSocket认证失败: ${data.message}`);\n        break;\n\n      case \"seat_status_update\":\n        this.handleSeatStatusUpdate(data.data);\n        break;\n\n      case \"reservation_update\":\n        this.handleReservationUpdate(data.data);\n        break;\n\n      case \"reservation_reminder\":\n        this.handleReservationReminder(data.data);\n        break;\n\n      case \"system_notification\":\n        this.handleSystemNotification(data.data);\n        break;\n\n      case \"error\":\n        console.error(`WebSocket错误: ${name}`, data.message);\n        ElMessage.error(data.message);\n        break;\n\n      default:\n        if (options.onMessage) {\n          options.onMessage(data);\n        }\n        break;\n    }\n  }\n\n  /**\n   * 处理座位状态更新\n   * @param {Object} seatData - 座位数据\n   */\n  handleSeatStatusUpdate(seatData) {\n    // 更新Vuex中的座位状态\n    store.dispatch(\"seat/updateSeatStatus\", seatData);\n  }\n\n  /**\n   * 处理预约更新\n   * @param {Object} reservationData - 预约数据\n   */\n  handleReservationUpdate(reservationData) {\n    // 更新Vuex中的预约状态\n    store.dispatch(\"seat/updateReservationStatus\", reservationData);\n  }\n\n  /**\n   * 处理预约提醒\n   * @param {Object} reminderData - 提醒数据\n   */\n  handleReservationReminder(reminderData) {\n    ElMessage({\n      title: \"预约提醒\",\n      message: `您的预约即将开始，请及时签到`,\n      type: \"warning\",\n      duration: 10000,\n    });\n  }\n\n  /**\n   * 处理系统通知\n   * @param {Object} notificationData - 通知数据\n   */\n  handleSystemNotification(notificationData) {\n    ElMessage({\n      title: notificationData.title,\n      message: notificationData.message,\n      type: notificationData.type || \"info\",\n      duration: 5000,\n    });\n  }\n\n  /**\n   * 订阅座位状态\n   * @param {string} name - 连接名称\n   * @param {number} roomId - 房间ID\n   */\n  subscribeSeatStatus(name, roomId) {\n    const message = {\n      type: \"subscribe\",\n      channel: \"seat_status\",\n      room_id: roomId,\n    };\n    this.send(name, message);\n  }\n\n  /**\n   * 取消订阅座位状态\n   * @param {string} name - 连接名称\n   * @param {number} roomId - 房间ID\n   */\n  unsubscribeSeatStatus(name, roomId) {\n    const message = {\n      type: \"unsubscribe\",\n      channel: \"seat_status\",\n      room_id: roomId,\n    };\n    this.send(name, message);\n  }\n\n  /**\n   * 断开WebSocket连接\n   * @param {string} name - 连接名称\n   */\n  disconnect(name) {\n    const connectionInfo = this.connections.get(name);\n    if (connectionInfo) {\n      connectionInfo.ws.close(1000, \"主动断开连接\");\n      this.connections.delete(name);\n      this.reconnectAttempts.delete(name);\n    }\n  }\n\n  /**\n   * 断开所有WebSocket连接\n   */\n  disconnectAll() {\n    for (const name of this.connections.keys()) {\n      this.disconnect(name);\n    }\n  }\n\n  /**\n   * 检查是否应该重连\n   * @param {string} name - 连接名称\n   * @returns {boolean}\n   */\n  shouldReconnect(name) {\n    const attempts = this.reconnectAttempts.get(name) || 0;\n    return attempts < this.maxReconnectAttempts;\n  }\n\n  /**\n   * 安排重连\n   * @param {string} name - 连接名称\n   */\n  scheduleReconnect(name) {\n    const connectionInfo = this.connections.get(name);\n    if (!connectionInfo) return;\n\n    const attempts = this.reconnectAttempts.get(name) || 0;\n    this.reconnectAttempts.set(name, attempts + 1);\n\n    console.log(`安排WebSocket重连: ${name}, 尝试次数: ${attempts + 1}`);\n\n    setTimeout(() => {\n      if (this.connections.has(name)) {\n        this.connect(connectionInfo.url, name, connectionInfo.options);\n      }\n    }, this.reconnectInterval * (attempts + 1));\n  }\n\n  /**\n   * 获取连接状态\n   * @param {string} name - 连接名称\n   * @returns {number|null}\n   */\n  getConnectionState(name) {\n    const connectionInfo = this.connections.get(name);\n    return connectionInfo ? connectionInfo.ws.readyState : null;\n  }\n\n  /**\n   * 检查连接是否已认证\n   * @param {string} name - 连接名称\n   * @returns {boolean}\n   */\n  isAuthenticated(name) {\n    const connectionInfo = this.connections.get(name);\n    return connectionInfo ? connectionInfo.isAuthenticated : false;\n  }\n}\n\n// 创建全局WebSocket管理器实例\nconst wsManager = new WebSocketManager();\n\nexport default wsManager;\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,cAAc;AACxC,OAAOC,KAAK,MAAM,SAAS;AAE3B,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC;IAClC,IAAI,CAACE,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAACC,GAAG,EAAEC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,IAAI,CAACR,WAAW,CAACS,GAAG,CAACF,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACG,UAAU,CAACH,IAAI,CAAC;IACvB;IAEA,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVG,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC;MACtC,OAAO,IAAI;IACb;IAEA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACX,GAAG,CAAC;IAC7B,MAAMY,cAAc,GAAG;MACrBF,EAAE;MACFV,GAAG;MACHE,OAAO;MACPW,eAAe,EAAE,KAAK;MACtBC,YAAY,EAAE;IAChB,CAAC;IAED,IAAI,CAACpB,WAAW,CAACqB,GAAG,CAACd,IAAI,EAAEW,cAAc,CAAC;IAC1C,IAAI,CAAChB,iBAAiB,CAACmB,GAAG,CAACd,IAAI,EAAE,CAAC,CAAC;;IAEnC;IACAS,EAAE,CAACM,MAAM,GAAG,MAAM;MAChBR,OAAO,CAACS,GAAG,CAAC,mBAAmBhB,IAAI,EAAE,CAAC;;MAEtC;MACA,IAAI,CAACiB,YAAY,CAACjB,IAAI,EAAEI,KAAK,CAAC;MAE9B,IAAIH,OAAO,CAACiB,MAAM,EAAE;QAClBjB,OAAO,CAACiB,MAAM,CAAC,CAAC;MAClB;IACF,CAAC;;IAED;IACAT,EAAE,CAACU,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;QACnC,IAAI,CAACG,aAAa,CAACxB,IAAI,EAAEqB,IAAI,CAAC;MAChC,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;;IAED;IACAC,EAAE,CAACgB,OAAO,GAAIL,KAAK,IAAK;MACtBb,OAAO,CAACS,GAAG,CAAC,mBAAmBhB,IAAI,EAAE,EAAEoB,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,MAAM,CAAC;MAEhE,IAAI1B,OAAO,CAAC2B,OAAO,EAAE;QACnB3B,OAAO,CAAC2B,OAAO,CAACR,KAAK,CAAC;MACxB;;MAEA;MACA,IAAIA,KAAK,CAACM,IAAI,KAAK,IAAI,IAAI,IAAI,CAACG,eAAe,CAAC7B,IAAI,CAAC,EAAE;QACrD,IAAI,CAAC8B,iBAAiB,CAAC9B,IAAI,CAAC;MAC9B;IACF,CAAC;;IAED;IACAS,EAAE,CAACsB,OAAO,GAAIvB,KAAK,IAAK;MACtBD,OAAO,CAACC,KAAK,CAAC,kBAAkBR,IAAI,EAAE,EAAEQ,KAAK,CAAC;MAE9C,IAAIP,OAAO,CAAC+B,OAAO,EAAE;QACnB/B,OAAO,CAAC+B,OAAO,CAACxB,KAAK,CAAC;MACxB;IACF,CAAC;IAED,OAAOC,EAAE;EACX;;EAEA;AACF;AACA;AACA;AACA;EACEQ,YAAYA,CAACjB,IAAI,EAAEI,KAAK,EAAE;IACxB,MAAMO,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,IAAI,CAACW,cAAc,EAAE;IAErB,MAAMuB,WAAW,GAAG;MAClBC,IAAI,EAAE,cAAc;MACpB/B,KAAK,EAAEA;IACT,CAAC;IAED,IAAI,CAACgC,IAAI,CAACpC,IAAI,EAAEkC,WAAW,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;EACEE,IAAIA,CAACpC,IAAI,EAAEqC,OAAO,EAAE;IAClB,MAAM1B,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,IAAI,CAACW,cAAc,EAAE;MACnBJ,OAAO,CAACC,KAAK,CAAC,mBAAmBR,IAAI,EAAE,CAAC;MACxC,OAAO,KAAK;IACd;IAEA,MAAM;MAAES,EAAE;MAAEG,eAAe;MAAEC;IAAa,CAAC,GAAGF,cAAc;IAE5D,IAAIF,EAAE,CAAC6B,UAAU,KAAK5B,SAAS,CAAC6B,IAAI,EAAE;MACpC,IAAIF,OAAO,CAACF,IAAI,KAAK,cAAc,IAAIvB,eAAe,EAAE;QACtDH,EAAE,CAAC2B,IAAI,CAACd,IAAI,CAACkB,SAAS,CAACH,OAAO,CAAC,CAAC;QAChC,OAAO,IAAI;MACb,CAAC,MAAM;QACL;QACAxB,YAAY,CAAC4B,IAAI,CAACJ,OAAO,CAAC;QAC1B,OAAO,KAAK;MACd;IACF,CAAC,MAAM;MACL9B,OAAO,CAACC,KAAK,CAAC,mBAAmBR,IAAI,EAAE,CAAC;MACxC,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEwB,aAAaA,CAACxB,IAAI,EAAEqB,IAAI,EAAE;IACxB,MAAMV,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,IAAI,CAACW,cAAc,EAAE;IAErB,MAAM;MAAEV,OAAO;MAAEY;IAAa,CAAC,GAAGF,cAAc;IAEhD,QAAQU,IAAI,CAACc,IAAI;MACf,KAAK,wBAAwB;QAC3B5B,OAAO,CAACS,GAAG,CAAC,oBAAoBhB,IAAI,EAAE,CAAC;QACvC;MAEF,KAAK,cAAc;QACjBO,OAAO,CAACS,GAAG,CAAC,kBAAkBhB,IAAI,EAAE,CAAC;QACrCW,cAAc,CAACC,eAAe,GAAG,IAAI;;QAErC;QACA,OAAOC,YAAY,CAAC6B,MAAM,GAAG,CAAC,EAAE;UAC9B,MAAMC,aAAa,GAAG9B,YAAY,CAAC+B,KAAK,CAAC,CAAC;UAC1C,IAAI,CAACR,IAAI,CAACpC,IAAI,EAAE2C,aAAa,CAAC;QAChC;QAEA,IAAI1C,OAAO,CAAC4C,eAAe,EAAE;UAC3B5C,OAAO,CAAC4C,eAAe,CAACxB,IAAI,CAAC;QAC/B;QACA;MAEF,KAAK,YAAY;QACfd,OAAO,CAACC,KAAK,CAAC,kBAAkBR,IAAI,EAAE,EAAEqB,IAAI,CAACgB,OAAO,CAAC;QACrDhD,SAAS,CAACmB,KAAK,CAAC,kBAAkBa,IAAI,CAACgB,OAAO,EAAE,CAAC;QACjD;MAEF,KAAK,oBAAoB;QACvB,IAAI,CAACS,sBAAsB,CAACzB,IAAI,CAACA,IAAI,CAAC;QACtC;MAEF,KAAK,oBAAoB;QACvB,IAAI,CAAC0B,uBAAuB,CAAC1B,IAAI,CAACA,IAAI,CAAC;QACvC;MAEF,KAAK,sBAAsB;QACzB,IAAI,CAAC2B,yBAAyB,CAAC3B,IAAI,CAACA,IAAI,CAAC;QACzC;MAEF,KAAK,qBAAqB;QACxB,IAAI,CAAC4B,wBAAwB,CAAC5B,IAAI,CAACA,IAAI,CAAC;QACxC;MAEF,KAAK,OAAO;QACVd,OAAO,CAACC,KAAK,CAAC,gBAAgBR,IAAI,EAAE,EAAEqB,IAAI,CAACgB,OAAO,CAAC;QACnDhD,SAAS,CAACmB,KAAK,CAACa,IAAI,CAACgB,OAAO,CAAC;QAC7B;MAEF;QACE,IAAIpC,OAAO,CAACiD,SAAS,EAAE;UACrBjD,OAAO,CAACiD,SAAS,CAAC7B,IAAI,CAAC;QACzB;QACA;IACJ;EACF;;EAEA;AACF;AACA;AACA;EACEyB,sBAAsBA,CAACK,QAAQ,EAAE;IAC/B;IACA7D,KAAK,CAAC8D,QAAQ,CAAC,uBAAuB,EAAED,QAAQ,CAAC;EACnD;;EAEA;AACF;AACA;AACA;EACEJ,uBAAuBA,CAACM,eAAe,EAAE;IACvC;IACA/D,KAAK,CAAC8D,QAAQ,CAAC,8BAA8B,EAAEC,eAAe,CAAC;EACjE;;EAEA;AACF;AACA;AACA;EACEL,yBAAyBA,CAACM,YAAY,EAAE;IACtCjE,SAAS,CAAC;MACRkE,KAAK,EAAE,MAAM;MACblB,OAAO,EAAE,gBAAgB;MACzBF,IAAI,EAAE,SAAS;MACfqB,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACEP,wBAAwBA,CAACQ,gBAAgB,EAAE;IACzCpE,SAAS,CAAC;MACRkE,KAAK,EAAEE,gBAAgB,CAACF,KAAK;MAC7BlB,OAAO,EAAEoB,gBAAgB,CAACpB,OAAO;MACjCF,IAAI,EAAEsB,gBAAgB,CAACtB,IAAI,IAAI,MAAM;MACrCqB,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEE,mBAAmBA,CAAC1D,IAAI,EAAE2D,MAAM,EAAE;IAChC,MAAMtB,OAAO,GAAG;MACdF,IAAI,EAAE,WAAW;MACjByB,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAEF;IACX,CAAC;IACD,IAAI,CAACvB,IAAI,CAACpC,IAAI,EAAEqC,OAAO,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;EACEyB,qBAAqBA,CAAC9D,IAAI,EAAE2D,MAAM,EAAE;IAClC,MAAMtB,OAAO,GAAG;MACdF,IAAI,EAAE,aAAa;MACnByB,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAEF;IACX,CAAC;IACD,IAAI,CAACvB,IAAI,CAACpC,IAAI,EAAEqC,OAAO,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;EACElC,UAAUA,CAACH,IAAI,EAAE;IACf,MAAMW,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,IAAIW,cAAc,EAAE;MAClBA,cAAc,CAACF,EAAE,CAACsD,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC;MACvC,IAAI,CAACtE,WAAW,CAACuE,MAAM,CAAChE,IAAI,CAAC;MAC7B,IAAI,CAACL,iBAAiB,CAACqE,MAAM,CAAChE,IAAI,CAAC;IACrC;EACF;;EAEA;AACF;AACA;EACEiE,aAAaA,CAAA,EAAG;IACd,KAAK,MAAMjE,IAAI,IAAI,IAAI,CAACP,WAAW,CAACyE,IAAI,CAAC,CAAC,EAAE;MAC1C,IAAI,CAAC/D,UAAU,CAACH,IAAI,CAAC;IACvB;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE6B,eAAeA,CAAC7B,IAAI,EAAE;IACpB,MAAMmE,QAAQ,GAAG,IAAI,CAACxE,iBAAiB,CAACsC,GAAG,CAACjC,IAAI,CAAC,IAAI,CAAC;IACtD,OAAOmE,QAAQ,GAAG,IAAI,CAACvE,oBAAoB;EAC7C;;EAEA;AACF;AACA;AACA;EACEkC,iBAAiBA,CAAC9B,IAAI,EAAE;IACtB,MAAMW,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,IAAI,CAACW,cAAc,EAAE;IAErB,MAAMwD,QAAQ,GAAG,IAAI,CAACxE,iBAAiB,CAACsC,GAAG,CAACjC,IAAI,CAAC,IAAI,CAAC;IACtD,IAAI,CAACL,iBAAiB,CAACmB,GAAG,CAACd,IAAI,EAAEmE,QAAQ,GAAG,CAAC,CAAC;IAE9C5D,OAAO,CAACS,GAAG,CAAC,kBAAkBhB,IAAI,WAAWmE,QAAQ,GAAG,CAAC,EAAE,CAAC;IAE5DC,UAAU,CAAC,MAAM;MACf,IAAI,IAAI,CAAC3E,WAAW,CAACS,GAAG,CAACF,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACF,OAAO,CAACa,cAAc,CAACZ,GAAG,EAAEC,IAAI,EAAEW,cAAc,CAACV,OAAO,CAAC;MAChE;IACF,CAAC,EAAE,IAAI,CAACJ,iBAAiB,IAAIsE,QAAQ,GAAG,CAAC,CAAC,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;EACEE,kBAAkBA,CAACrE,IAAI,EAAE;IACvB,MAAMW,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,OAAOW,cAAc,GAAGA,cAAc,CAACF,EAAE,CAAC6B,UAAU,GAAG,IAAI;EAC7D;;EAEA;AACF;AACA;AACA;AACA;EACE1B,eAAeA,CAACZ,IAAI,EAAE;IACpB,MAAMW,cAAc,GAAG,IAAI,CAAClB,WAAW,CAACwC,GAAG,CAACjC,IAAI,CAAC;IACjD,OAAOW,cAAc,GAAGA,cAAc,CAACC,eAAe,GAAG,KAAK;EAChE;AACF;;AAEA;AACA,MAAM0D,SAAS,GAAG,IAAI/E,gBAAgB,CAAC,CAAC;AAExC,eAAe+E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}